# 重启功能重构说明

## 概述

根据用户要求，已完成网络管理器重启部分的重构，主要改进包括：

1. **提供公共重启接口**：根据重启类型执行不同重启操作
2. **智能重启策略**：联网成功后为软重启，重启后联网仍失败再变为硬重启
3. **接口释放机制**：重启时释放/dev/ttyUSB1接口，避免设备枚举变为/dev/ttyUSB2

## 主要变更

### 1. NetworkManager 结构体变更

```rust
pub struct NetworkManager<T> {
    modem: Option<NetModem>,           // 改为 Option，支持释放和重建
    device_path: String,               // 新增：存储设备路径
    // ... 其他字段
    connection_successful: bool,       // 新增：跟踪连接成功状态
}
```

### 2. 新增公共重启接口

```rust
/// 公共重启接口 - 根据重启类型执行不同重启操作
pub async fn restart(&mut self, restart_type: RestartType) -> ModemResult<()>
```

**功能特点**：
- 支持指定重启类型（软重启/硬重启）
- 自动释放接口避免设备枚举问题
- 重启后自动重新创建连接
- 包含重启间隔和次数限制

### 3. 智能重启策略

```rust
/// 内部重启模块 - 智能选择重启类型
async fn restart_module(&mut self) -> ModemResult<()>
```

**策略逻辑**：
- **联网成功后**：优先使用软重启（前2次），失败后使用硬重启
- **从未联网成功**：直接使用硬重启
- 基于 `connection_successful` 标志判断连接历史

### 4. 接口管理机制

#### 释放接口
```rust
fn release_modem(&mut self) {
    if self.modem.is_some() {
        log::info!("释放 modem 连接");
        self.modem = None;
    }
}
```

#### 重新创建连接
```rust
async fn recreate_modem(&mut self) -> ModemResult<()> {
    log::info!("重新创建 modem 连接到 {}", self.device_path);
    
    // 等待设备重新枚举
    sleep(Duration::from_secs(3)).await;
    
    let modem = NetModem::new_from_path(&self.device_path).await?;
    self.modem = Some(modem);
    
    log::info!("Modem 连接重新创建成功");
    Ok(())
}
```

### 5. 重启流程改进

#### 软重启流程
1. 发送厂商特定的软重启命令
2. 立即释放 `/dev/ttyUSB1` 接口
3. 等待模块重新启动
4. 重新创建连接

#### 硬重启流程
1. 发送厂商特定的硬重启命令
2. 立即释放 `/dev/ttyUSB1` 接口
3. 等待模块重新启动
4. 重新创建连接

## 使用示例

### 手动重启
```rust
// 软重启
network_manager.restart(RestartType::Soft).await?;

// 硬重启
network_manager.restart(RestartType::Hard).await?;
```

### 自动重启（连接失败时）
```rust
// 连接失败时会自动调用智能重启
network_manager.connect().await?;
```

### 状态查询
```rust
// 获取连接成功状态
let is_successful = network_manager.is_connection_successful();

// 获取重启计数
let restart_count = network_manager.get_restart_count();

// 获取当前重启类型
let restart_type = network_manager.get_restart_type();

// 获取设备路径
let device_path = network_manager.get_device_path();
```

## 关键改进点

### 1. 解决设备枚举问题
- **问题**：重启时不释放接口，设备重新枚举为 `/dev/ttyUSB2`
- **解决**：重启前主动释放接口，确保设备仍为 `/dev/ttyUSB1`

### 2. 智能重启策略
- **问题**：不区分连接历史，重启策略不够智能
- **解决**：基于连接成功状态选择合适的重启类型

### 3. 接口生命周期管理
- **问题**：接口管理不够灵活
- **解决**：支持动态释放和重建连接

## 测试验证

程序启动后会提供重启功能测试选项：

```
🔧 测试重启功能...
是否要测试重启功能？(y/N): y
测试软重启...
✅ 软重启成功
设备路径: /dev/ttyUSB1
✅ 重启后重新连接成功
```

## 兼容性

- 保持原有 API 兼容性
- 内部重启逻辑完全重构
- 支持所有厂商配置（ZTE、Quectel等）

## 注意事项

1. 重启操作会暂时断开网络连接
2. 重启间隔和次数限制仍然有效
3. 设备路径需要在配置文件中正确设置
4. 重启后需要重新进行完整的连接流程
