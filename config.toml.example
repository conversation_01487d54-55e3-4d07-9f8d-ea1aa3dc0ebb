# NetModule 配置文件示例
# 复制此文件为 config.toml 并根据需要修改

[pdp_context]
# PDP 上下文类型: "IPV4", "IPV6", "IPV4V6"
pdp_type = "IPV4"
# 接入点名称，请根据运营商提供的信息填写
apn = "internet"

# 用于连接测试的IP地址列表
connectivity_test_ips = [
    "*******",           # Google DNS
    "***************",   # 114 DNS
    "*******"            # Cloudflare DNS
]

# 拨号超时时间（秒）
dial_timeout = 60

# 重连间隔时间（秒）
reconnect_interval = 30

# 最大重连尝试次数
max_reconnect_attempts = 5

# AT命令超时时间（毫秒）
at_command_timeout = 5000

[serial_config]
# 串口设备路径
device_path = "/dev/ttyUSB1"

# 波特率
baud_rate = 9600

# 数据位
data_bits = 8

# 停止位
stop_bits = 1

# 校验位: "none", "odd", "even"
parity = "none" 