# AT命令解析器修复总结

## 问题描述

在解析 `+CGPADDR: 1,************` 响应时，解析器错误地将IP地址 `************` 解析为：
```
Array([Inte<PERSON>(1), Integer(10)])
```

而不是期望的：
```
Array([Integer(1), Unknown("************")])
```

## 根本原因

问题出现在 `src/core/parse.rs` 的 `parse_single_value` 函数中，解析器的优先级顺序为：

1. `parse_bracketed_array`
2. `parse_string` 
3. `parse_range`
4. **`parse_integer`** ← 问题所在
5. `parse_unknown`
6. `parse_empty`

当遇到 `************` 时：
- `parse_integer` 使用 `many1(one_of("0123456789"))` 成功解析出 `10`
- 遇到 `.` 字符时停止，返回部分成功的结果
- 剩余的 `.92.79.114` 被丢弃

## 解决方案

修改 `parse_integer` 函数，添加前瞻检查：

```rust
pub fn parse_integer(input: &[u8]) -> IResult<&[u8], u32> {
    // 使用前瞻检查，确保数字后面不是点号，避免将IP地址的第一部分误解析为整数
    let (remaining, digits) = many1(one_of("0123456789")).parse(input)?;
    
    // 检查数字后面是否跟着点号，如果是则拒绝解析为整数
    if !remaining.is_empty() && remaining[0] == b'.' {
        return Err(nom::Err::Error(nom::error::Error::new(input, nom::error::ErrorKind::Tag)));
    }
    
    let st: String = digits.into_iter().collect();
    match st.parse() {
        Ok(num) => Ok((remaining, num)),
        Err(_) => Err(nom::Err::Error(nom::error::Error::new(input, nom::error::ErrorKind::MapRes))),
    }
}
```

## 修复效果

### 修复前
```
+CGPADDR: 1,************
↓
Array([Integer(1), Integer(10)])  // ❌ 错误
```

### 修复后
```
+CGPADDR: 1,************
↓
Array([Integer(1), Unknown("************")])  // ✅ 正确
```

## 测试验证

添加了专门的测试用例来验证修复：

```rust
#[test]
fn test_cgpaddr_response_parsing() {
    let response = b"\r\n+CGPADDR: 1,************\r\n\r\nOK\r\n";
    let result = responses(response).unwrap();
    
    // 验证解析结果
    match &result.1[0] {
        AtResponse::InformationResponse { param, response } => {
            assert_eq!(param, "+CGPADDR");
            match response {
                AtValue::Array(values) => {
                    assert_eq!(values[0], AtValue::Integer(1));
                    assert_eq!(values[1], AtValue::Unknown("************".to_string()));
                }
                _ => panic!("Expected Array"),
            }
        }
        _ => panic!("Expected InformationResponse"),
    }
}
```

## 兼容性

修复保持了向后兼容性：

- ✅ 纯整数仍然正确解析：`123` → `Integer(123)`
- ✅ 整数范围正确解析：`2-9001` → `Range((2, 9001))`
- ✅ 混合数组正确解析：`1,************` → `Array([Integer(1), Unknown("************")])`
- ✅ 所有现有测试用例通过

## 影响范围

这个修复解决了所有包含IP地址、版本号或其他点分格式数据的AT命令响应解析问题，包括但不限于：

- `+CGPADDR` (IP地址分配)
- `+CGDCONT` (PDP上下文配置)
- 版本号信息
- 其他包含点分数值的响应

## 总结

通过在 `parse_integer` 中添加简单的前瞻检查，从根本上解决了IP地址等点分格式数据被误解析为整数的问题，同时保持了解析器的性能和兼容性。
