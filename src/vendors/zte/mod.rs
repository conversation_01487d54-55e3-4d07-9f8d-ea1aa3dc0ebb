use crate::core::{AtResponse, AtResultCode};
use crate::core::{
    AtCommand, AtResponsePacket, AtValue, ModemResult, PdpContext,
};
use crate::core::ModemError;
use crate::core::state::SignalQuality;
use crate::ok_handler;
use crate::{CmdHdlBuild, VendorConfig};
use crate::CommandHandler;
/// ZTE命令配置结构
#[derive(Clone)]
#[non_exhaustive]
pub struct ZTEConfig {
    reset_cmd: String,
    factory_reset_code: u32,
    connect_cmd: String,
    mode_cmd: String,
    hot_plug_cmd: String,
    ccid_cmd: String,
}

impl Default for ZTEConfig {
    fn default() -> Self {
        Self {
            reset_cmd: "+ZTURNOFF".to_string(),
            factory_reset_code: 6,
            connect_cmd: "+ZECMCALL".to_string(),
            mode_cmd: "AT+ZARFCN?".to_string(),
            hot_plug_cmd: "+ZSDT".to_string(),
            ccid_cmd: "ZGETICCID".to_string(),
        }
    }
}

impl CmdHdlBuild for ZTEConfig {
    fn reset_command(
        &self,
    ) -> CommandHandler<Box<dyn Fn(AtResponsePacket) -> ModemResult<()> + Send + Sync>, ()> {
        CommandHandler {
            cmd: AtCommand::Execute {
                command: self.reset_cmd.clone(),
            },
            handler: Box::new(ok_handler),
        }
    }
    fn factory_reset_command(
        &self,
    ) -> CommandHandler<Box<dyn Fn(AtResponsePacket) -> ModemResult<()> + Send + Sync>, ()> {
        CommandHandler {
            cmd: AtCommand::Equals {
                param: "+CFUN".to_string(),
                value: AtValue::Integer(self.factory_reset_code),
            },
            handler: Box::new(ok_handler),
        }
    }

    fn connect_command(
        &self,
        opt: Option<AtValue>,
    ) -> CommandHandler<Box<dyn Fn(AtResponsePacket) -> ModemResult<bool> + Send + Sync>, bool> {
        match opt {
            None => CommandHandler {
                cmd: AtCommand::Read {
                    param: self.connect_cmd.clone(),
                },
                handler: Box::new(|pkt: AtResponsePacket| -> ModemResult<bool> { 
                    log::trace!("connect command response: {:?}", pkt);
                    pkt.assert_ok()?;
                    let res = pkt.extract_named_response("+ZECMCALL")?;
                    let val = res.get_array()?;
                    let val = val.get(1).ok_or(ModemError::ParseError(format!("failed to get connect status from {}", res)))?;
                    Ok(matches!(val, AtValue::Unknown(_)))
                }),
            },
            Some(value) => CommandHandler {
                cmd: AtCommand::Equals {
                    param: self.connect_cmd.clone(),
                    value,
                },
                handler: Box::new(|pkt: AtResponsePacket| -> ModemResult<bool> {
                    Ok(pkt.extract_named_response("+ZECMCALL")?.get_unknown()?.as_str() == "CONNECT")
                }),
            },
        }
    }

    fn mode_command(&self) -> CommandHandler<Box<dyn Fn(AtResponsePacket) -> ModemResult<String> + Send + Sync>, String> {
        CommandHandler {
            cmd: AtCommand::Text {
                text: self.mode_cmd.clone(),
                expected: vec!["GSM","UMTS","LTE","1XLTE","1XDO","1X","EVDO"].iter().map(|s| s.to_string()).collect(),
            },
            handler: Box::new(|pkt: AtResponsePacket| -> ModemResult<String> {
                log::trace!("mode command response: {:?}", pkt);
                pkt.assert_ok()?;
                if let AtResponse::InformationResponse { param, response: _ } = pkt.responses.get(0).ok_or(ModemError::ParseError("failed to get mode from response".to_string()))? {
                    Ok(param.clone())
                } else {
                    Err(ModemError::ParseError("failed to get mode from response".to_string()))
                }
            }),
        }
    }

    fn hot_plug_command(&self, opt: Option<AtValue>) -> CommandHandler<Box<dyn Fn(AtResponsePacket) -> ModemResult<bool> + Send + Sync>, bool> {
        match opt {
            None => CommandHandler {
                cmd: AtCommand::Read {
                    param: self.hot_plug_cmd.clone(),
                },
                handler: Box::new(|pkt: AtResponsePacket| -> ModemResult<bool> {
                    log::trace!("hot plug command response: {:?}", pkt);
                    pkt.assert_ok()?;
                    let res = pkt.extract_named_response("+ZSDT")?;
                    let val = res.get_array()?;
                    Ok(*val.get(0).ok_or(ModemError::ParseError(format!("failed to get hot plug status from {}", res)))?.get_integer()? == 1)
                }),
            },
            Some(value) => CommandHandler {
                cmd: AtCommand::Equals {
                    param: self.hot_plug_cmd.clone(),
                    value,
                },
                handler: Box::new(|pkt: AtResponsePacket| -> ModemResult<bool> {
                    Ok(pkt.status == AtResultCode::Ok)
                }),
            }
        }
    }

    fn signal_quality_command(&self) -> CommandHandler<Box<dyn Fn(AtResponsePacket) -> ModemResult<SignalQuality> + Send + Sync>, SignalQuality> {
        CommandHandler {
            cmd: AtCommand::Read {
                param: "+ZSRVRSP".to_string(),
            },
            handler: Box::new(|packet: AtResponsePacket| -> ModemResult<SignalQuality> {
                log::trace!("ZTE signal quality command response: {:?}", packet);
                packet.assert_ok()?;
                let val = packet.extract_named_response("+ZSRVRSP")?;
                let arr = val.get_array()?;

                // 解析 RSRP (第一个值，通常是字符串格式)
                let rsrp = match arr.get(0).ok_or(ModemError::ParseError(format!("failed to get rsrp from {}", val)))? {
                    AtValue::String(s) => s.parse::<i16>().map_err(|_| ModemError::ParseError(format!("failed to parse rsrp '{}' as i16", s)))?,
                    AtValue::Integer(i) => *i as i16,
                    _ => return Err(ModemError::ParseError(format!("unexpected rsrp value type in {}", val))),
                };

                // 解析 SINR (最后一个值，可能是字符串或整数格式)
                let sinr = match arr.last().ok_or(ModemError::ParseError(format!("failed to get sinr from {}", val)))? {
                    AtValue::Unknown(s) => s.parse::<i16>().map_err(|_| ModemError::ParseError(format!("failed to parse sinr '{}' as i16", s)))?,
                    AtValue::Integer(i) => *i as i16,
                    _ => return Err(ModemError::ParseError(format!("unexpected sinr value type in {}", val))),
                };

                Ok(SignalQuality {
                    rsrp,
                    sinr,
                })
            }),
        }
    }
}

impl VendorConfig for ZTEConfig {
    fn vendor_name(&self) -> &str {
        "ZTE"
    }

    fn supported_models(&self) -> Vec<&str> {
        vec![
            "ME3630-W",
        ]
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::core::{AtResponse, AtResponsePacket, AtResultCode, AtValue};

    #[test]
    fn test_zte_signal_quality_parsing() {
        let zte_config = ZTEConfig::default();
        let signal_handler = zte_config.signal_quality_command();

        // 测试用例1: +ZSRVRSP:"-129","-14",-4
        let test_packet1 = AtResponsePacket {
            responses: vec![AtResponse::InformationResponse {
                param: "+ZSRVRSP".to_string(),
                response: AtValue::Array(vec![
                    AtValue::Unknown("-129".to_string()),  // RSRP as string
                    AtValue::Unknown("-14".to_string()),   // 中间值 as string
                    AtValue::Unknown("-4".to_string()),    // SINR as string (负数用字符串表示)
                ]),
            }],
            status: AtResultCode::Ok,
        };

        let result1 = (signal_handler.handler)(test_packet1);
        assert!(result1.is_ok(), "解析 +ZSRVRSP:\"-129\",\"-14\",-4 失败: {:?}", result1.err());

        let signal_quality1 = result1.unwrap();
        assert_eq!(signal_quality1.rsrp, -129, "RSRP 解析错误");
        assert_eq!(signal_quality1.sinr, -4, "SINR 解析错误");

        // 测试用例2: +ZSRVRSP:"-91","-8",14
        let signal_handler2 = zte_config.signal_quality_command();
        let test_packet2 = AtResponsePacket {
            responses: vec![AtResponse::InformationResponse {
                param: "+ZSRVRSP".to_string(),
                response: AtValue::Array(vec![
                    AtValue::Unknown("-91".to_string()),   // RSRP as string
                    AtValue::Unknown("-8".to_string()),    // 中间值 as string
                    AtValue::Integer(14),                   // SINR as integer
                ]),
            }],
            status: AtResultCode::Ok,
        };

        let result2 = (signal_handler2.handler)(test_packet2);
        assert!(result2.is_ok(), "解析 +ZSRVRSP:\"-91\",\"-8\",14 失败: {:?}", result2.err());

        let signal_quality2 = result2.unwrap();
        assert_eq!(signal_quality2.rsrp, -91, "RSRP 解析错误");
        assert_eq!(signal_quality2.sinr, 14, "SINR 解析错误");
    }

    #[test]
    fn test_zte_signal_quality_all_string_format() {
        let zte_config = ZTEConfig::default();
        let signal_handler = zte_config.signal_quality_command();

        // 测试所有值都是字符串格式的情况
        let test_packet = AtResponsePacket {
            responses: vec![AtResponse::InformationResponse {
                param: "+ZSRVRSP".to_string(),
                response: AtValue::Array(vec![
                    AtValue::Unknown("-100".to_string()),  // RSRP as string
                    AtValue::Unknown("5".to_string()),     // 中间值 as string
                    AtValue::Unknown("-2".to_string()),    // SINR as string
                ]),
            }],
            status: AtResultCode::Ok,
        };

        let result = (signal_handler.handler)(test_packet);
        assert!(result.is_ok(), "解析全字符串格式失败: {:?}", result.err());

        let signal_quality = result.unwrap();
        assert_eq!(signal_quality.rsrp, -100, "RSRP 解析错误");
        assert_eq!(signal_quality.sinr, -2, "SINR 解析错误");
    }

    #[test]
    fn test_zte_signal_quality_all_integer_format() {
        let zte_config = ZTEConfig::default();
        let signal_handler = zte_config.signal_quality_command();

        // 测试所有值都是整数格式的情况（正数）
        let test_packet = AtResponsePacket {
            responses: vec![AtResponse::InformationResponse {
                param: "+ZSRVRSP".to_string(),
                response: AtValue::Array(vec![
                    AtValue::Integer(90),   // RSRP as positive integer
                    AtValue::Integer(8),    // 中间值 as integer
                    AtValue::Integer(12),   // SINR as integer
                ]),
            }],
            status: AtResultCode::Ok,
        };

        let result = (signal_handler.handler)(test_packet);
        assert!(result.is_ok(), "解析全整数格式失败: {:?}", result.err());

        let signal_quality = result.unwrap();
        assert_eq!(signal_quality.rsrp, 90, "RSRP 解析错误");
        assert_eq!(signal_quality.sinr, 12, "SINR 解析错误");
    }

    #[test]
    fn test_zte_signal_quality_error_cases() {
        let zte_config = ZTEConfig::default();
        let signal_handler = zte_config.signal_quality_command();

        // 测试空数组的情况
        let test_packet_empty = AtResponsePacket {
            responses: vec![AtResponse::InformationResponse {
                param: "+ZSRVRSP".to_string(),
                response: AtValue::Array(vec![]),
            }],
            status: AtResultCode::Ok,
        };

        let result_empty = (signal_handler.handler)(test_packet_empty);
        assert!(result_empty.is_err(), "空数组应该返回错误");

        // 测试无效字符串的情况
        let signal_handler2 = zte_config.signal_quality_command();
        let test_packet_invalid = AtResponsePacket {
            responses: vec![AtResponse::InformationResponse {
                param: "+ZSRVRSP".to_string(),
                response: AtValue::Array(vec![
                    AtValue::Unknown("invalid".to_string()),  // 无效的RSRP
                    AtValue::Unknown("-8".to_string()),
                    AtValue::Integer(14),
                ]),
            }],
            status: AtResultCode::Ok,
        };

        let result_invalid = (signal_handler2.handler)(test_packet_invalid);
        assert!(result_invalid.is_err(), "无效字符串应该返回错误");
    }
}