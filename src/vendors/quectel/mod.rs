//! Quectel调制解调器实现
//!
//! 这个模块包含了Quectel调制解调器的特定实现。

use crate::core::{AtResponse, AtResultCode};
use crate::core::{
    AtCommand, AtResponsePacket, AtValue, ModemResult, PdpContext,
};
use crate::core::ModemError;
use crate::core::state::SignalQuality;
use crate::ok_handler;
use crate::{CmdHdlBuild, VendorConfig};
use crate::CommandHandler;

/// Quectel命令配置结构
#[derive(Clone)]
#[non_exhaustive]
pub struct QuectelConfig {
    reset_cmd: String,
    factory_reset_code: u32,
    connect_cmd: String,
    mode_cmd: String,
    hot_plug_cmd: String,
}

impl Default for QuectelConfig {
    fn default() -> Self {
        Self {
            reset_cmd: "+QPOWD".to_string(),
            factory_reset_code: 1,
            connect_cmd: "+QNETDEVCTL".to_string(),
            mode_cmd: "+QNWINFO".to_string(),
            hot_plug_cmd: "+QSIMDET".to_string(),
        }
    }
}

impl CmdHdlBuild for QuectelConfig {
    fn reset_command(&self) -> CommandHandler<Box<dyn Fn(AtResponsePacket) -> ModemResult<()> + Send + Sync>, ()> {
        CommandHandler {
            cmd: AtCommand::Equals {
                param: self.reset_cmd.clone(),
                value: AtValue::Integer(1),
            },
            handler: Box::new(ok_handler),
        }
    }

    fn factory_reset_command(&self) -> CommandHandler<Box<dyn Fn(AtResponsePacket) -> ModemResult<()> + Send + Sync>, ()> {
        CommandHandler {
            cmd: AtCommand::Equals {
                param: "+CFUN".to_string(),
                value: AtValue::Integer(self.factory_reset_code),
            },
            handler: Box::new(ok_handler),
        }
    }

    fn connect_command(&self, opt: Option<AtValue>) -> CommandHandler<Box<dyn Fn(AtResponsePacket) -> ModemResult<bool> + Send + Sync>, bool> {
        match opt {
            None => CommandHandler {
                cmd: AtCommand::Read {
                    param: self.connect_cmd.clone(),
                },
                handler: Box::new(|pkt: AtResponsePacket| -> ModemResult<bool> {
                    log::trace!("Quectel connect command response: {:?}", pkt);
                    pkt.assert_ok()?;
                    // TODO: 实现Quectel特定的连接状态解析
                    Ok(true)
                }),
            },
            Some(value) => CommandHandler {
                cmd: AtCommand::Equals {
                    param: self.connect_cmd.clone(),
                    value,
                },
                handler: Box::new(|pkt: AtResponsePacket| -> ModemResult<bool> {
                    Ok(pkt.status == AtResultCode::Ok)
                }),
            },
        }
    }

    fn mode_command(&self) -> CommandHandler<Box<dyn Fn(AtResponsePacket) -> ModemResult<String> + Send + Sync>, String> {
        CommandHandler {
            cmd: AtCommand::Read {
                param: self.mode_cmd.clone(),
            },
            handler: Box::new(|pkt: AtResponsePacket| -> ModemResult<String> {
                log::trace!("Quectel mode command response: {:?}", pkt);
                pkt.assert_ok()?;
                // TODO: 实现Quectel特定的模式解析
                Ok("LTE".to_string())
            }),
        }
    }

    fn hot_plug_command(&self, opt: Option<AtValue>) -> CommandHandler<Box<dyn Fn(AtResponsePacket) -> ModemResult<bool> + Send + Sync>, bool> {
        match opt {
            None => CommandHandler {
                cmd: AtCommand::Read {
                    param: self.hot_plug_cmd.clone(),
                },
                handler: Box::new(|pkt: AtResponsePacket| -> ModemResult<bool> {
                    log::trace!("Quectel hot plug command response: {:?}", pkt);
                    pkt.assert_ok()?;
                    // TODO: 实现Quectel特定的热插拔状态解析
                    Ok(true)
                }),
            },
            Some(value) => CommandHandler {
                cmd: AtCommand::Equals {
                    param: self.hot_plug_cmd.clone(),
                    value,
                },
                handler: Box::new(|pkt: AtResponsePacket| -> ModemResult<bool> {
                    Ok(pkt.status == AtResultCode::Ok)
                }),
            }
        }
    }

    fn signal_quality_command(&self) -> CommandHandler<Box<dyn Fn(AtResponsePacket) -> ModemResult<SignalQuality> + Send + Sync>, SignalQuality> {
        CommandHandler {
            cmd: AtCommand::Execute {
                command: "+CSQ".to_string(),
            },
            handler: Box::new(|packet: AtResponsePacket| -> ModemResult<SignalQuality> {
                log::trace!("Quectel signal quality command response: {:?}", packet);
                packet.assert_ok()?;
                let val = packet.extract_named_response("+CSQ")?;
                let arr = val.get_array()?;
                let rssi = *arr.get(0).ok_or(ModemError::ParseError(format!("failed to get csq from {}", val)))?.get_integer()?;
                if rssi == 99 {
                    return Err(ModemError::Other("no signal".to_string()));
                }
                // Quectel 使用 CSQ 命令，只有 RSSI，SINR 设为 0
                Ok(SignalQuality {
                    rsrp: rssi as i16,
                    sinr: 0,
                })
            }),
        }
    }
}

// NetworkCmdBuild 方法现在已经合并到 CmdHdlBuild 中
// TODO: 需要在 CmdHdlBuild 实现中添加这些方法

impl VendorConfig for QuectelConfig {
    fn vendor_name(&self) -> &str {
        "Quectel"
    }

    fn supported_models(&self) -> Vec<&str> {
        vec![
            "EC25",
            "EC20",
            "EG25-G",
            "EM12-G",
            "RM500Q",
            "RG500Q",
            "AG35",
        ]
    }
}