pub mod core;
pub mod vendors;
pub mod dbus;

// 重新导出常用类型
pub use vendors::zte::ZTEConfig;
pub use vendors::quectel::QuectelConfig;
pub use dbus::DbusService;

use crate::core::{AtCommand, AtResponsePacket, AtValue, Modem, ModemResult, PdpContext};
pub struct CommandHandler<F, R>
where F: Fn(AtResponsePacket) -> ModemResult<R> + Send + Sync + 'static
{
    pub cmd: AtCommand,
    pub handler: F,
}

/// 完整的厂商配置trait，组合了基础命令和网络命令
pub trait VendorConfig: CmdHdlBuild + Send + Sync + Clone + 'static {
    /// 获取厂商名称
    fn vendor_name(&self) -> &str;

    /// 获取支持的模块型号列表
    fn supported_models(&self) -> Vec<&str>;
}

pub trait CmdHdlBuild {
    fn reset_command(&self) -> CommandHandler<Box<dyn Fn(AtResponsePacket) -> ModemResult<()> + Send + Sync>, ()>;
    fn factory_reset_command(&self) -> CommandHandler<Box<dyn Fn(AtResponsePacket) -> ModemResult<()> + Send + Sync>, ()>;
    fn connect_command(&self, opt: Option<AtValue>) -> CommandHandler<Box<dyn Fn(AtResponsePacket) -> ModemResult<bool> + Send + Sync>, bool>;
    fn mode_command(&self) -> CommandHandler<Box<dyn Fn(AtResponsePacket) -> ModemResult<String> + Send + Sync>, String>;
    fn hot_plug_command(&self, opt: Option<AtValue>) -> CommandHandler<Box<dyn Fn(AtResponsePacket) -> ModemResult<bool> + Send + Sync>, bool>;
    fn signal_quality_command(&self) -> CommandHandler<Box<dyn Fn(AtResponsePacket) -> ModemResult<crate::core::state::SignalQuality> + Send + Sync>, crate::core::state::SignalQuality>;
    fn setup_pdp_context_command(&self, pdp_context: &PdpContext) -> CommandHandler<Box<dyn Fn(AtResponsePacket) -> ModemResult<()> + Send + Sync>, ()> {
        let pdp_cmd = format!("1,\"{}\",\"{}\"", pdp_context.pdp_type, pdp_context.apn);
        CommandHandler {
            cmd: AtCommand::Equals {
                param: "+CGDCONT".to_string(),
                value: AtValue::Unknown(pdp_cmd),
            },
            handler: Box::new(|packet| {
                log::trace!("PDP context setup response: {:?}", packet);
                packet.assert_ok()
            }),
        }
    }
}

#[async_trait::async_trait]
pub trait VendorCmd: Modem {
    async fn reset_command(&self) -> ModemResult<()>;
    async fn factory_reset_command(&self) -> ModemResult<()>;
    async fn connect_command(&self, opt: Option<AtValue>) -> ModemResult<bool>;
    async fn mode_command(&self) -> ModemResult<String>;
    async fn hot_plug_command(&self, opt: Option<AtValue>) -> ModemResult<bool>;
    async fn signal_quality_command(&self) -> ModemResult<crate::core::state::SignalQuality>;
    async fn setup_pdp_context_command(&self, pdp_context: &PdpContext) -> ModemResult<()> {
        let pdp_cmd = format!("1,\"{}\",\"{}\"", pdp_context.pdp_type, pdp_context.apn);
        let h = CommandHandler {
            cmd: AtCommand::Equals {
                param: "+CGDCONT".to_string(),
                value: AtValue::Unknown(pdp_cmd),
            },
            handler: Box::new(|packet: AtResponsePacket| {
                log::trace!("PDP context setup response: {:?}", packet);
                packet.assert_ok()
            }),
        };
        self.send_with_handler(h.cmd, h.handler).await
    }
}

pub fn ok_handler(packet: AtResponsePacket) -> ModemResult<()> {
    packet.assert_ok()
}