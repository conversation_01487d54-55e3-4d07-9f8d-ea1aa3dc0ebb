use env_logger;
use log::{info, error, warn};
use netmodule::core::{AtResponse, ConfigManager, ModemError, ModemInterface, ModemManager, NetModem, RestartType, StateManager};
use netmodule::{ZTEConfig, DbusService};
use std::sync::Arc;
use std::time::Duration;
use tokio::time::sleep;

/// 检测模块类型并更新状态，返回是否为ZTE模块
async fn detect_module_and_update_state(mut modem: NetModem, state_manager: &StateManager) -> Result<(NetModem, bool), Box<dyn std::error::Error>> {
    info!("🔍 检测模块类型...");

    // 发送ATI命令获取模块信息
    match modem.get_information().await {
        Ok(info) => {
            info!("📋 模块信息获取成功");

            let mut manufacturer = String::new();
            let mut model = String::new();
            let mut revision = String::new();
            let mut imei = String::new();

            // 解析ATI响应
            for response in &info.responses {
                if let AtResponse::InformationResponse { param, response } = response {
                    match param.as_str() {
                        "Manufacturer" => {
                            if let Ok(value) = response.get_unknown() {
                                manufacturer = value.clone();
                                info!("  制造商: {}", manufacturer);
                            }
                        }
                        "Model" => {
                            if let Ok(value) = response.get_unknown() {
                                model = value.clone();
                                info!("  型号: {}", model);
                            }
                        }
                        "Revision" => {
                            if let Ok(value) = response.get_unknown() {
                                revision = value.clone();
                                info!("  版本: {}", revision);
                            }
                        }
                        "IMEI" => {
                            if let Ok(value) = response.get_unknown() {
                                imei = value.clone();
                                info!("  IMEI: {}", imei);
                            }
                        }
                        _ => {}
                    }
                }
            }

            // 立即更新状态管理器
            state_manager.update_state(|state| {
                state.set_model(model.clone());
                state.set_revision(revision.clone());
                state.set_imei(imei.clone());
                None
            });

            // 根据制造商和型号判断是否为ZTE模块
            let is_zte = if manufacturer.contains("GOSUNCNWELINK") ||
                            model.contains("ME3630") {
                info!("✅ 检测到ZTE模块");
                true
            } else if manufacturer.contains("Quectel") ||
                      model.contains("EC25") || model.contains("EC20") ||
                      model.contains("EG25") || model.contains("EM12") ||
                      model.contains("RM500") || model.contains("RG500") {
                info!("✅ 检测到Quectel模块");
                false
            } else {
                info!("⚠️  未识别的模块类型，使用默认ZTE配置");
                info!("   制造商: {}, 型号: {}", manufacturer, model);
                true
            };

            Ok((modem, is_zte))
        }
        Err(e) => {
            error!("❌ 模块信息获取失败: {}", e);
            info!("⚠️  使用默认ZTE配置");
            Ok((modem, true))
        }
    }
}

#[tokio::main]
async fn main() {
    env_logger::init();

    info!("🚀 网络模块测试程序启动");
    info!("================================\n");
    
    let config_manager = ConfigManager::new("/etc/netmodule/config.toml".to_string());
    config_manager.load().expect("should not fail to load config");
    let state_manager = StateManager::new();
    // 初始化调制解调器
    info!("📡 初始化网络模块组件...");
    let stream = config_manager.get_config().get_serial_config().open_stream().expect("Failed to open serial stream");
    let modem = match NetModem::new_from_stream(stream).await {
        Ok(modem) => {
            info!("✅ 调制解调器连接成功");
            modem
        }
        Err(e) => {
            error!("❌ 调制解调器连接失败: {}", e);
            return;
        }
    };

    // TODO: move modem ownship to VenderModem type
    // 检测模块类型并更新状态
    let (modem, _is_zte) = match detect_module_and_update_state(modem, &state_manager).await {
        Ok((modem, is_zte)) => (modem, is_zte),
        Err(e) => {
            error!("❌ 模块检测失败: {}", e);
            return;
        }
    };

    // 创建网络管理器 - 目前使用ZTE配置
    // TODO: 根据检测结果选择不同的厂商配置
    let zte_config = ZTEConfig::default();
    let network_manager = ModemManager::new(modem, config_manager.clone(), state_manager.clone(), zte_config);
    info!("✅ 网络管理器创建成功");

    // 启动DBus服务作为子进程
    info!("🚌 启动DBus服务...");
    let shared_manager = Arc::new(tokio::sync::Mutex::new(network_manager));
    let mut dbus_service = DbusService::with_shared_network_manager(shared_manager.clone(), config_manager, state_manager);

    let _dbus_handle = tokio::spawn(async move {
        if let Err(e) = dbus_service.start().await {
            log::error!("DBus服务启动失败: {}", e);
        } else {
            log::info!("DBus服务启动成功");
            // 保持服务运行
            loop {
                tokio::time::sleep(Duration::from_secs(1)).await;
            }
        }
    });

    info!("✅ DBus服务已启动\n");

    // 显示初始诊断信息
    info!("📊 初始诊断信息:");
    {
        let manager_guard = shared_manager.lock().await;
        info!("{}\n", manager_guard.generate_diagnostic_info().await);
    }

    // 启动网络连接和持续监控
    info!("🔗 开始网络连接和持续监控...");

    // 网络管理主循环线程
    let manager_for_loop = shared_manager.clone();
    let manager_thread = tokio::spawn(async move {

        info!("🔄 启动网络管理主循环");
        let mut consecutive_failures = 0;
        let max_consecutive_failures = 3;
        let monitoring_interval = Duration::from_secs(30);

        loop {
            info!("\n📡 开始新的网络管理周期...");

            // 使用异步块来处理网络管理逻辑
            let result = async {
                info!("📡 检查 modem 连接状态...");
                {
                    let mut manager_guard = manager_for_loop.lock().await;
                    manager_guard.check_device_availability().await?;
                }

                info!("🌐 尝试建立网络连接...");
                {
                    let mut manager_guard = manager_for_loop.lock().await;
                    manager_guard.connect().await?;
                }
                
                info!("👁️  开始状态监控 (间隔: {:?})", monitoring_interval);

                // 监控循环 - 持续监控直到出现错误
                loop {
                    {
                        let manager_guard = manager_for_loop.lock().await;
                        info!("   {}", manager_guard.generate_diagnostic_info().await);
                    }
                    sleep(monitoring_interval).await;

                    {
                        let mut manager_guard = manager_for_loop.lock().await;
                        match manager_guard.monitor_status().await {
                            Ok(()) => {
                                info!("✅ 状态监控正常");
                            }
                            Err(e) => {
                                warn!("⚠️  状态监控检测到问题: {}", e);
                                consecutive_failures += 1;
                                // 监控失败，返回错误以跳出监控循环
                                return Err(e);
                            }
                        }
                    }
                }
            }.await;

            match result {
                Ok(()) => {
                    info!("网络管理周期正常完成");
                    consecutive_failures = 0;
                }
                Err(e) => {
                    warn!("❌ 网络管理周期失败: {}", e);
                    consecutive_failures += 1;

                    // 根据失败次数决定重启策略
                    if consecutive_failures >= max_consecutive_failures {
                        info!("💥 连续失败次数过多，执行重启");
                        {
                            let mut manager_guard = manager_for_loop.lock().await;
                            if let Err(restart_err) = manager_guard.restart_module().await {
                                error!("❌ 重启失败: {}", restart_err);
                            }
                        }

                        consecutive_failures = 0;
                    }
                }
            }

            // 等待一段时间后重试
            println!("⏳ 等待 10 秒后进入下一个周期...");
            sleep(Duration::from_secs(10)).await;
        }
    });

    // 等待管理线程完成
    if let Err(e) = manager_thread.await {
        error!("❌ 网络管理线程异常退出: {}", e);
    }
}
