use crate::core::gps::Location;
// use std::collections::HashMap;
use std::sync::{Arc, RwLock};

#[derive(Debug, <PERSON><PERSON>)]
pub struct SignalQuality {
    pub rsrp: i16,
    pub sinr: i16,
}

#[derive(Debu<PERSON>, <PERSON><PERSON>)]
pub enum RegistrationState {
    NotRegistered = 0,
    Registered = 1,
    Searching = 2,
    Denied = 3,
    Unknown = 4,
}

impl RegistrationState {
    pub fn from_value(val: u32) -> Self {
        match val {
            0 => Self::NotRegistered,
            1 => Self::Registered,
            2 => Self::Searching,
            3 => Self::Denied,
            4 => Self::Unknown,
            _ => Self::Unknown,
        }
    }

    pub fn as_str(&self) -> &str {
        match self {
            Self::NotRegistered => "未注册",
            Self::Registered => "已注册",
            Self::Searching => "正在搜索",
            Self::Denied => "注册被拒",
            Self::Unknown => "未知",
        }
    }
}

#[derive(Debu<PERSON>, <PERSON><PERSON>)]
pub enum ConnectionState {
    Disconnected,
    Connecting,
    Connected,
    Disconnecting,
    Error,
}

impl ConnectionState {
    pub fn as_str(&self) -> &str {
        match self {
            Self::Disconnected => "disconnected",
            Self::Connecting => "connecting", 
            Self::Connected => "connected",
            Self::Disconnecting => "disconnecting",
            Self::Error => "error",
        }
    }
    
    pub fn from_str(s: &str) -> Self {
        match s {
            "disconnected" => Self::Disconnected,
            "connecting" => Self::Connecting,
            "connected" => Self::Connected,
            "disconnecting" => Self::Disconnecting,
            "error" => Self::Error,
            _ => Self::Disconnected,
        }
    }
}

#[derive(Debug, Clone)]
pub struct ModemState {
    // DBus属性对应的字段
    sim: String,           // SIM卡状态
    ccid: String,          // 卡号
    oper: String,          // 运营商
    model: String,         // 模块型号
    revision: String,      // 模块版本
    imei: String,          // 国际移动设备识别号
    mode: String,          // 网络制式
    rsrp: i16,            // 信号质量
    sinr: i16,            // 信噪比
    location: Location,    // 经纬度
    state: ConnectionState, // 模块连接状态
    
    // 内部状态
    signal_quality: SignalQuality,
    imsi: String,
    registration_state: RegistrationState,
}

impl Default for ModemState {
    fn default() -> Self {
        Self {
            sim: String::new(),
            ccid: String::new(),
            oper: String::new(),
            model: String::new(),
            revision: String::new(),
            imei: String::new(),
            mode: String::new(),
            rsrp: 0,
            sinr: 0,
            location: Location::default(),
            state: ConnectionState::Disconnected,
            signal_quality: SignalQuality { rsrp: 0, sinr: 0 },
            imsi: String::new(),
            registration_state: RegistrationState::Unknown,
        }
    }
}

impl ModemState {
    pub fn new() -> Self {
        Self::default()
    }
    
    // DBus属性的getter方法
    pub fn sim(&self) -> &str {
        &self.sim
    }
    
    pub fn ccid(&self) -> &str {
        &self.ccid
    }
    
    pub fn oper(&self) -> &str {
        &self.oper
    }
    
    pub fn model(&self) -> &str {
        &self.model
    }
    
    pub fn revision(&self) -> &str {
        &self.revision
    }
    
    pub fn imei(&self) -> &str {
        &self.imei
    }
    
    pub fn mode(&self) -> &str {
        &self.mode
    }
    
    pub fn rsrp(&self) -> i16 {
        self.rsrp
    }
    
    pub fn sinr(&self) -> i16 {
        self.sinr
    }
    
    pub fn location(&self) -> (u32, u32) {
        (self.location.latitude as u32, self.location.longitude as u32)
    }
    
    pub fn state(&self) -> &str {
        self.state.as_str()
    }
    
    // Setter方法
    pub fn set_sim(&mut self, sim: String) {
        self.sim = sim;
    }
    
    pub fn set_ccid(&mut self, ccid: String) {
        self.ccid = ccid;
    }

    pub fn set_rsrp(&mut self, rsrp: i16) {
        self.rsrp = rsrp;
        self.signal_quality.rsrp = rsrp;
    }

    pub fn set_sinr(&mut self, sinr: i16) {
        self.sinr = sinr;
        self.signal_quality.sinr = sinr;
    }
    
    pub fn set_oper(&mut self, oper: String) {
        self.oper = oper;
    }
    
    pub fn set_model(&mut self, model: String) {
        self.model = model;
    }
    
    pub fn set_revision(&mut self, revision: String) {
        self.revision = revision;
    }
    
    pub fn set_imei(&mut self, imei: String) {
        self.imei = imei;
    }
    
    pub fn set_mode(&mut self, mode: String) {
        self.mode = mode;
    }
    
    pub fn set_signal_quality(&mut self, rsrp: i16, sinr: i16) {
        self.rsrp = rsrp;
        self.sinr = sinr;
        self.signal_quality = SignalQuality { rsrp, sinr };
    }
    
    pub fn set_location(&mut self, latitude: f64, longitude: f64) {
        self.location = Location { latitude, longitude };
    }
    
    pub fn set_state(&mut self, state: ConnectionState) -> Option<String> {
        let old_state = self.state.clone();
        self.state = state;
        
        // 如果状态发生变化，返回状态变化信息
        if std::mem::discriminant(&old_state) != std::mem::discriminant(&self.state) {
            Some(format!("{} -> {}", old_state.as_str(), self.state.as_str()))
        } else {
            None
        }
    }
    
    // 内部状态的getter方法
    pub fn signal_quality(&self) -> &SignalQuality {
        &self.signal_quality
    }
    
    pub fn imsi(&self) -> &str {
        &self.imsi
    }
    
    pub fn set_imsi(&mut self, imsi: String) {
        self.imsi = imsi;
    }
    
    pub fn registration_state(&self) -> &RegistrationState {
        &self.registration_state
    }
    
    pub fn set_registration_state(&mut self, state: RegistrationState) {
        self.registration_state = state;
    }
    
    // 诊断信息生成
    pub fn generate_diag_info(&self) -> String {
        format!(
            "Model: {}, Revision: {}, IMEI: {}, SIM: {}, CCID: {}, Operator: {}, Mode: {}, RSRP: {}, SINR: {}, State: {}, Location: ({}, {})",
            self.model, self.revision, self.imei, self.sim, self.ccid, self.oper, 
            self.mode, self.rsrp, self.sinr, self.state.as_str(), 
            self.location.latitude, self.location.longitude
        )
    }
}

// 状态管理器，用于线程安全的状态访问
#[derive(Clone)]
pub struct StateManager {
    state: Arc<RwLock<ModemState>>,
}

impl StateManager {
    pub fn new() -> Self {
        Self {
            state: Arc::new(RwLock::new(ModemState::new())),
        }
    }
    
    pub fn get_state(&self) -> ModemState {
        self.state.read().unwrap().clone()
    }
    
    pub fn update_state<F>(&self, update_fn: F) -> Option<String>
    where
        F: FnOnce(&mut ModemState) -> Option<String>,
    {
        let mut state = self.state.write().unwrap();
        update_fn(&mut *state)
    }
    
    pub fn get_state_ref(&self) -> Arc<RwLock<ModemState>> {
        Arc::clone(&self.state)
    }
}

impl Default for StateManager {
    fn default() -> Self {
        Self::new()
    }
}