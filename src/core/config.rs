use std::collections::HashMap;
use std::sync::{Arc, RwLock};
use std::time::Duration;
use serde::{Deserialize, Serialize};
use tokio_serial::SerialPortBuilderExt;
use crate::core::ModemError;
use log::warn;

/// PDP上下文配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PdpContext {
    pub pdp_type: String,  // 如 "IPV4", "IPV6", "IPV4V6"
    pub apn: String,       // 接入点名称
}

impl Default for PdpContext {
    fn default() -> Self {
        Self {
            pdp_type: "IP".to_string(),
            apn: "internet".to_string(),
        }
    }
}

/// 网络模块配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NetModuleConfig {
    /// PDP上下文配置
    pub pdp_context: PdpContext,
    
    /// 用于通信模组联网状态判断的IP列表
    pub connectivity_test_ips: Vec<String>,
    
    /// 拨号超时时间（秒）
    pub dial_timeout: u32,
    
    /// 重连间隔时间（秒）
    pub reconnect_interval: u32,
    
    /// 最大重连尝试次数
    pub max_reconnect_attempts: u32,
    
    /// AT命令超时时间（毫秒）
    pub at_command_timeout: u32,
    
    /// 串口配置
    pub serial_config: SerialConfig,
}

/// 串口配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SerialConfig {
    /// 串口设备路径
    pub device_path: String,
    
    /// 波特率
    pub baud_rate: u32,
}

impl Default for SerialConfig {
    fn default() -> Self {
        Self {
            device_path: "/dev/ttyUSB1".to_string(),
            baud_rate: 9600,
        }
    }
}
impl SerialConfig {
    pub fn open_stream(&self) -> Result<tokio_serial::SerialStream, ModemError> {
        let serial = tokio_serial::new(&self.device_path, self.baud_rate);
        let stream = serial.open_native_async()?;
        Ok(stream)
    }
}

impl Default for NetModuleConfig {
    fn default() -> Self {
        Self {
            pdp_context: PdpContext::default(),
            connectivity_test_ips: vec![
                "************".to_string(),
                "*********".to_string(),
                "************".to_string(),
            ],
            dial_timeout: 60,
            reconnect_interval: 30,
            max_reconnect_attempts: 5,
            at_command_timeout: 5000,
            serial_config: SerialConfig::default(),
        }
    }
}

impl NetModuleConfig {
    /// 创建新的配置实例
    pub fn new() -> Self {
        Self::default()
    }
    
    /// 从文件加载配置
    pub fn from_file(path: &str) -> Result<Self, Box<dyn std::error::Error>> {
        let content = std::fs::read_to_string(path)?;
        let config: NetModuleConfig = toml::from_str(&content)?;
        Ok(config)
    }
    
    /// 保存配置到文件
    pub fn save_to_file(&self, path: &str) -> Result<(), Box<dyn std::error::Error>> {
        let content = toml::to_string_pretty(self)?;
        std::fs::write(path, content)?;
        Ok(())
    }
    
    /// 验证配置的有效性
    pub fn validate(&self) -> Result<(), String> {
        // 验证PDP类型
        if !["IPV4", "IPV6", "IPV4V6"].contains(&self.pdp_context.pdp_type.as_str()) {
            return Err(format!("Invalid PDP type: {}", self.pdp_context.pdp_type));
        }
        
        // 验证APN不为空
        if self.pdp_context.apn.is_empty() {
            return Err("APN cannot be empty".to_string());
        }
        
        // 验证连接测试IP不为空
        if self.connectivity_test_ips.is_empty() {
            return Err("Connectivity test IPs cannot be empty".to_string());
        }
        
        // 验证IP地址格式
        for ip in &self.connectivity_test_ips {
            if ip.parse::<std::net::IpAddr>().is_err() {
                return Err(format!("Invalid IP address: {}", ip));
            }
        }
        
        // 验证超时时间
        if self.dial_timeout == 0 {
            return Err("Dial timeout must be greater than 0".to_string());
        }
        
        if self.at_command_timeout == 0 {
            return Err("AT command timeout must be greater than 0".to_string());
        }
        
        // 验证串口设备路径
        if self.serial_config.device_path.is_empty() {
            return Err("Serial device path cannot be empty".to_string());
        }
        
        // 验证波特率
        if self.serial_config.baud_rate == 0 {
            return Err("Baud rate must be greater than 0".to_string());
        }
        
        Ok(())
    }
    
    // Getter 方法
    pub fn get_pdp_context(&self) -> &PdpContext {
        &self.pdp_context
    }
    
    pub fn get_connectivity_test_ips(&self) -> &Vec<String> {
        &self.connectivity_test_ips
    }
    
    pub fn get_dial_timeout(&self) -> u32 {
        self.dial_timeout
    }
    
    pub fn get_reconnect_interval(&self) -> u32 {
        self.reconnect_interval
    }
    
    pub fn get_max_reconnect_attempts(&self) -> u32 {
        self.max_reconnect_attempts
    }
    
    pub fn get_at_command_timeout(&self) -> u32 {
        self.at_command_timeout
    }
    
    pub fn get_serial_config(&self) -> &SerialConfig {
        &self.serial_config
    }
    
    // Setter 方法
    pub fn set_pdp_context(&mut self, pdp_context: PdpContext) {
        self.pdp_context = pdp_context;
    }
    
    pub fn set_connectivity_test_ips(&mut self, ips: Vec<String>) {
        self.connectivity_test_ips = ips;
    }
    
    pub fn set_dial_timeout(&mut self, timeout: u32) {
        self.dial_timeout = timeout;
    }
    
    pub fn set_reconnect_interval(&mut self, interval: u32) {
        self.reconnect_interval = interval;
    }
    
    pub fn set_max_reconnect_attempts(&mut self, attempts: u32) {
        self.max_reconnect_attempts = attempts;
    }
    
    pub fn set_at_command_timeout(&mut self, timeout: u32) {
        self.at_command_timeout = timeout;
    }
    
    pub fn set_serial_config(&mut self, config: SerialConfig) {
        self.serial_config = config;
    }
    
    /// 转换为DBus属性格式（HashMap）
    pub fn to_dbus_pdp_context(&self) -> HashMap<String, String> {
        let mut map = HashMap::new();
        map.insert("pdp_type".to_string(), self.pdp_context.pdp_type.clone());
        map.insert("apn".to_string(), self.pdp_context.apn.clone());
        map
    }
    
    /// 从DBus属性格式更新PDP上下文
    pub fn update_from_dbus_pdp_context(&mut self, properties: HashMap<String, String>) -> Result<(), String> {
        if let Some(pdp_type) = properties.get("pdp_type") {
            if !["IPV4", "IPV6", "IPV4V6"].contains(&pdp_type.as_str()) {
                return Err(format!("Invalid PDP type: {}", pdp_type));
            }
            self.pdp_context.pdp_type = pdp_type.clone();
        }
        
        if let Some(apn) = properties.get("apn") {
            if apn.is_empty() {
                return Err("APN cannot be empty".to_string());
            }
            self.pdp_context.apn = apn.clone();
        }
        
        Ok(())
    }
}

/// 配置管理器，用于线程安全的配置访问
#[derive(Clone)]
pub struct ConfigManager {
    config: Arc<RwLock<NetModuleConfig>>,
    config_path: String,
}

impl ConfigManager {
    /// 创建新的配置管理器
    pub fn new(config_path: String) -> Self {
        Self {
            config: Arc::new(RwLock::new(NetModuleConfig::new())),
            config_path,
        }
    }
    
    /// 加载配置文件
    pub fn load(&self) -> Result<(), Box<dyn std::error::Error>> {
        let config = NetModuleConfig::from_file(&self.config_path)
            .and_then(|config| {
                config.validate()?;
                Ok(config)
            }).unwrap_or_else(|e| {
                log::warn!("Failed to load config from {}: {}, use default", self.config_path, e);
                NetModuleConfig::default()
            });
        
        let mut current_config = self.config.write().unwrap();
        *current_config = config;

        Ok(())
    }
    
    /// 保存配置文件
    pub fn save(&self) -> Result<(), Box<dyn std::error::Error>> {
        let config = self.config.read().unwrap();
        config.save_to_file(&self.config_path)
    }
    
    /// 获取配置副本
    pub fn get_config(&self) -> NetModuleConfig {
        self.config.read().unwrap().clone()
    }
    
    /// 更新配置
    pub fn update_config<F>(&self, update_fn: F) -> Result<(), Box<dyn std::error::Error>>
    where
        F: FnOnce(&mut NetModuleConfig),
    {
        let mut config = self.config.write().unwrap();
        update_fn(&mut *config);
        config.validate()?;
        Ok(())
    }
    
    /// 获取配置引用（用于其他模块访问）
    pub fn get_config_ref(&self) -> Arc<RwLock<NetModuleConfig>> {
        Arc::clone(&self.config)
    }
    
    /// 重置为默认配置
    pub fn reset_to_default(&self) -> Result<(), Box<dyn std::error::Error>> {
        let mut config = self.config.write().unwrap();
        *config = NetModuleConfig::default();
        config.save_to_file(&self.config_path)?;
        Ok(())
    }
}
