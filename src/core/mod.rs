//! 核心公用模块
//!
//! 提供通用的AT命令处理基础设施，可被不同厂商的调制解调器实现复用。

pub mod at;
pub mod codec;
pub mod config;
pub mod errors;
pub mod future;
mod gps;
pub mod modem;
pub mod parse;
pub mod state;
pub mod traits;

pub use self::at::{AtCommand, AtResponse, AtResponsePacket, AtResultCode, AtValue};
pub use self::codec::AtCodec;
pub use self::config::{ConfigManager, NetModuleConfig, PdpContext, SerialConfig};
pub use self::errors::{ModemError, ModemResult};
pub use self::future::{ModemFuture, ModemRequest, ModemResponse};
pub use self::modem::{ModemManager, RestartType, RetryConfig};
pub use self::state::{ConnectionState, ModemState, RegistrationState, SignalQuality, StateManager};
pub use self::traits::ModemInterface;
use async_trait::async_trait;
use futures::FutureExt;
use std::path::Path;
use tokio::sync::{mpsc, oneshot, Mutex};
use tokio_serial::frame::SerialFramed;
use tokio_serial::SerialStream;

/// Future representing a response from the modem.
pub struct ModemResponseFuture {
    pub rx: oneshot::Receiver<ModemResponse>,
}

impl std::future::Future for ModemResponseFuture {
    type Output = Result<ModemResponse, ModemError>;

    fn poll(
        mut self: std::pin::Pin<&mut Self>,
        cx: &mut std::task::Context<'_>,
    ) -> std::task::Poll<Self::Output> {
        // use std::pin::Pin;
        // Pin::new(&mut self.rx).poll(cx).map_err(|_| ModemError::FutureDied)
        self.rx.poll_unpin(cx).map_err(|_| ModemError::FutureDied)
    }
}

/// A connection to an AT/Huawei-style modem.
pub struct NetModem {
    pub tx: mpsc::UnboundedSender<ModemRequest>,
    urc: Option<mpsc::UnboundedReceiver<AtResponse>>,
}

impl NetModem {
    /// Start talking to the modem represented by a given stream.
    pub async fn new_from_stream(stream: SerialStream) -> ModemResult<Self> {
        let framed = SerialFramed::new(stream, AtCodec);
        let (tx, rx) = mpsc::unbounded_channel();
        let (urctx, urcrx) = mpsc::unbounded_channel();
        let fut = ModemFuture::new(framed, rx, urctx);

        tokio::spawn(async move {
            if let Err(e) = fut.await {
                log::error!("ModemFuture failed: {}", e);
            }
        });

        Ok(Self {
            tx: tx,
            urc: Some(urcrx),
        })
    }

    /// Retrieve the URC (Unsolicited Result Code) receiver from the modem (it can only be taken
    /// once).
    ///
    /// This gives you an `UnboundedReceiver` that provides you with a stream of `AtResponse`s that
    /// are *unsolicited*, i.e. they are proactive notifications from the modem of something
    /// happening. On some modems, you may well receive a steady stream of random updates.
    ///
    /// This can be useful when you configure your modem for message notification on delivery (see
    /// `cmd::sms::set_new_message_indications`), in which case you'll want to check for `CNMI`
    /// URCs through this receiver and use that to poll for new messages.
    pub fn take_urc_rx(&mut self) -> Option<mpsc::UnboundedReceiver<AtResponse>> {
        self.urc.take()
    }
}
#[async_trait]
pub trait Modem {
    async fn send_raw(&self, cmd: AtCommand) -> Result<ModemResponse, ModemError>;
    async fn send_with_handler<F, R>(
        &self,
        cmd: AtCommand,
        handler: F,
    ) -> Result<R, ModemError>
    where
        F: Fn(AtResponsePacket) -> Result<R, ModemError> + Send;
}
#[async_trait]
impl Modem for NetModem {
    async fn send_raw(&self, cmd: AtCommand) -> Result<ModemResponse, ModemError> {
        let (tx, rx) = oneshot::channel();
        let expected = cmd.expected();
        let req = ModemRequest {
            command: cmd,
            notif: tx,
            expected,
        };
        match self.tx.send(req) {
            Ok(_) => {
                log::trace!("Request sent to modem");
                ModemResponseFuture { rx }.await
            },
            Err(e) => {
                // Create a closed receiver to represent the error
                log::error!("Failed to send request to modem");
                Err(ModemError::Other(format!("Failed to send request to modem, {}", e)))
            }
        }
}

    async fn send_with_handler<F, R>(&self, cmd: AtCommand, handler: F) -> Result<R, ModemError>
    where
        F: Fn(AtResponsePacket) -> Result<R, ModemError> + Send,
    {
        let r = self.send_raw(cmd.clone()).await?;
        // r.assert_ok()?;
        match handler(r.clone()) {
            Ok(r) => Ok(r),
            Err(e) => {
                log::warn!("handler returned error: {} for command: {:?} with response: {:?}", e, cmd, r);
                Err(e)
            }
        }
    }
}

#[async_trait]
impl ModemInterface for NetModem {
    // 使用默认实现
}
