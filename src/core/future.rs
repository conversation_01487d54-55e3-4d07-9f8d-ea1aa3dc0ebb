use crate::core::at::{AtCommand, AtResponse, AtResponsePacket, AtResultCode};
use crate::core::codec::AtCodec;
use crate::core::errors::ModemError;
use futures::{FutureExt, SinkExt, StreamExt};
use tokio::sync::{mpsc, oneshot};
use tokio_serial::frame::SerialFramed;

pub type ModemResponse = AtResponsePacket;

pub struct ModemRequest {
    pub(crate) command: AtCommand,
    pub(crate) expected: Vec<String>,
    pub(crate) notif: oneshot::Sender<ModemResponse>,
}

struct ModemRequestState {
    notif: oneshot::Sender<ModemResponse>,
    expected: Vec<String>,
    responses: Vec<AtResponse>,
}

pub struct ModemFuture {
    inner: SerialFramed<AtCodec>,
    rx: mpsc::UnboundedReceiver<ModemRequest>,
    urc: mpsc::UnboundedSender<AtResponse>,
    cur: Option<ModemRequestState>,
    requests: Vec<ModemRequest>,
    fresh: bool,
}

impl ModemFuture {
    pub(crate) fn new(
        inner: SerialFramed<AtCodec>,
        rx: mpsc::UnboundedReceiver<ModemRequest>,
        urc: mpsc::UnboundedSender<AtResponse>,
    ) -> Self {
        Self {
            inner,
            rx,
            urc,
            cur: None,
            requests: vec![],
            fresh: true,
        }
    }

    async fn handle_responses(&mut self, responses: Vec<AtResponse>) -> Result<(), ModemError> {
        if let Some(mut state) = self.cur.take() {
            if responses.iter().any(|x| x.is_result_code()) {
                state.responses.extend(responses);
                log::debug!("request completed with responses: {:?}", state.responses);

                let mut resps = vec![];
                let mut status = None;

                for resp in state.responses {
                    match resp {
                        AtResponse::InformationResponse { param, response } => {
                            if state.expected.contains(&param) {
                                resps.push(AtResponse::InformationResponse { param, response });
                            } else {
                                let _ = self
                                    .urc
                                    .send(AtResponse::InformationResponse { param, response });
                            }
                        }
                        AtResponse::ResultCode(x) => {
                            status = Some(x);
                        }
                        x => resps.push(x),
                    }
                }

                let _ = state.notif.send(AtResponsePacket {
                    responses: resps,
                    status: status.unwrap(),
                });
            } else {
                log::trace!("new responses: {:?}", responses);
                state.responses.extend(responses);
                self.cur = Some(state);
            }
        } else {
            for resp in responses {
                let _ = self.urc.send(resp);
            }
        }

        Ok(())
    }
}

impl std::future::Future for ModemFuture {
    type Output = Result<(), ModemError>;

    fn poll(
        mut self: std::pin::Pin<&mut Self>,
        cx: &mut std::task::Context<'_>,
    ) -> std::task::Poll<Self::Output> {
        use std::task::Poll;

        if self.fresh {
            self.fresh = false;
            log::debug!("first poll of future, imposing initial settings");
            let (tx, _) = oneshot::channel();
            let echo = AtCommand::Basic {
                command: "E".into(),
                number: Some(0),
            };
            self.requests.insert(
                0,
                ModemRequest {
                    command: echo,
                    expected: vec![],
                    notif: tx,
                },
            );
        }

        loop {
            // Handle incoming responses from the modem
            match self.inner.poll_next_unpin(cx) {
                Poll::Ready(Some(Ok(responses))) => {
                    if let Err(e) = self.handle_responses(responses).now_or_never().unwrap() {
                        return Poll::Ready(Err(e));
                    }
                    // 处理完响应后继续循环，重新注册waker
                    continue;
                }
                Poll::Ready(Some(Err(e))) => {
                    log::error!("Error reading from modem: {}", e);
                    return Poll::Ready(Err(e));
                }
                Poll::Ready(None) => {
                    log::debug!("stream ran out, future exiting");
                    return Poll::Ready(Ok(()));
                }
                Poll::Pending => {}
            }

            // Handle new requests from the application
            match self.rx.poll_recv(cx) {
                Poll::Ready(Some(req)) => {
                    log::debug!("got a new request: {:?}", req.command);
                    self.requests.push(req);
                }
                Poll::Ready(None) => {
                    log::debug!("receiver ran out, future exiting");
                    return Poll::Ready(Ok(()));
                }
                Poll::Pending => {}
            }

            // Process pending requests if no current request is being handled
            if self.cur.is_none() && !self.requests.is_empty() {
                let req = self.requests.remove(0);
                log::debug!("starting new request: {:?}", req.command);

                match self.inner.start_send_unpin(req.command.to_string()) {
                    Ok(()) => {
                        self.cur = Some(ModemRequestState {
                            notif: req.notif,
                            expected: req.expected,
                            responses: vec![],
                        });
                    }
                    Err(e) => {
                        log::error!("Failed to send command: {}", e);
                        let _ = req.notif.send(AtResponsePacket {
                            responses: vec![],
                            status: AtResultCode::Error,
                        });
                        continue;
                    }
                }
            }

            // Flush the sink
            match self.inner.poll_flush_unpin(cx) {
                Poll::Ready(Ok(())) => {}
                Poll::Ready(Err(e)) => {
                    log::error!("Failed to flush sink: {}", e);
                    return Poll::Ready(Err(e));
                }
                Poll::Pending => return Poll::Pending,
            }

            // If nothing is ready, we're pending
            return Poll::Pending;
        }
    }
}
