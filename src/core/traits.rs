//! 调制解调器接口定义
//!
//! 定义了所有调制解调器实现都应该遵循的通用接口。

use crate::core::Modem;
use crate::core::{AtCommand, AtResponse, AtResponsePacket, AtValue, ModemError, ModemResult};
use async_trait::async_trait;

use crate::core::state::RegistrationState;

/// 调制解调器基础接口

/// 所有调制解调器实现都应该实现此trait
#[async_trait]
pub trait ModemInterface: Modem {
    async fn get_information(&self) -> ModemResult<AtResponsePacket> {
        let r = self
            .send_raw(AtCommand::Text {
                text: "ATI".to_string(),
                expected: vec!["Manufacturer", "Model", "Revision", "IMEI"]
                    .iter()
                    .map(|s| s.to_string())
                    .collect(),
            })
            .await?;
        r.assert_ok()?;
        Ok(r)
    }

    async fn get_cfun(&self) -> ModemResult<u32> {
        self.send_with_handler(
            AtCommand::Read {
                param: "+CFUN".to_string(),
            },
            |packet| {
                let val = packet.extract_named_response("+CFUN")?;
                Ok(*val.get_integer()?)
            },
        )
        .await
    }

    /// 获取网络注册状态
    async fn get_registration_state(&self) -> ModemResult<RegistrationState> {
        self.send_with_handler(
            AtCommand::Read {
                param: "+CREG".to_string(),
            },
            |packet| {
                let val = packet.extract_named_response("+CREG")?;
                Ok(RegistrationState::from_value(
                    *val.get_array()?
                        .get(1)
                        .ok_or(ModemError::ParseError(format!(
                            "failed to get registration state from {}",
                            val
                        )))?
                        .get_integer()?,
                ))
            }
        )
        .await
    }

    /// 获取网络运营商
    async fn get_operator(&self) -> ModemResult<String> {
        let r = self
            .send_raw(AtCommand::Read {
                param: "+COPS".to_string(),
            })
            .await?;

        let val = r.extract_named_response("+COPS")?;
        Ok(val
            .get_array()?
            .get(2)
            .ok_or(ModemError::ParseError(format!(
                "failed to get operator from {}",
                val
            )))?
            .get_string()?
            .to_string())
    }

    async fn check_sim(&self) -> ModemResult<String> {
        let r = self.send_raw(AtCommand::Read {
            param: "+CPIN".to_string(),
        }).await?;
        let val = r.extract_named_response("+CPIN")?;
        if r.assert_ok().is_ok() {
            Ok(val.get_unknown()?.as_str().to_string())
        } else {
            Err(ModemError::Other(format!("SIM卡状态异常: {:?}", val.get_unknown()?.as_str().to_string())))
        }
    }

    async fn check_csq(&self) -> ModemResult<u32> {
        self.send_with_handler(
            AtCommand::Execute {
                command: "+CSQ".to_string(),
            },
            |packet| {
                let val = packet.extract_named_response("+CSQ")?;
                let rssi = *val.get_array()?.get(0).ok_or(ModemError::ParseError(format!("failed to get csq from {}", val)))?.get_integer()?;
                if rssi == 99 {
                    return Err(ModemError::Other("no signal".to_string()));
                }
                Ok(rssi)
            }
        ).await
    }
}
