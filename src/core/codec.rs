//! AT协议编解码器
//! 
//! 提供AT命令和响应的编解码功能。

use tokio_util::codec::{Decoder, Encoder};
use bytes::{BytesMut, Buf};
use crate::core::errors::ModemError;
use crate::core::at::AtResponse;
use crate::core::parse;

/// AT协议编解码器
pub struct AtCodec;

impl Decoder for AtCodec {
    type Item = Vec<AtResponse>;
    type Error = ModemError;

    fn decode(&mut self, src: &mut BytesMut) -> Result<Option<Self::Item>, Self::Error> {
        log::trace!("decoding data: {:?}", src);
        if src.is_empty() {
            return Ok(None);
        }
        let (consumed, data) = match parse::responses(src) {
            Ok((rest, data)) => {
                if data.is_empty() {
                    return Ok(None);
                }
                (src.len() - rest.len(), data)
            },
            Err(nom::Err::Incomplete(_)) => return Ok(None),
            Err(e) => return Err(e.into()),
        };
        src.advance(consumed);
        Ok(Some(data))
    }
}

impl Encoder<String> for AtCodec {
    type Error = ModemError;

    fn encode(&mut self, item: String, buf: &mut BytesMut) -> Result<(), Self::Error> {
        log::trace!("encoding {}", item);
        buf.extend_from_slice(b"\r\n");
        buf.extend_from_slice(item.as_bytes());
        buf.extend_from_slice(b"\r\n");
        Ok(())
    }
}