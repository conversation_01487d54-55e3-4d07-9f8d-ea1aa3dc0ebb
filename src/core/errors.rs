//! 通用调制解调器错误处理
//! 
//! 定义了所有调制解调器实现都可以使用的通用错误类型。

use thiserror::Error;
use crate::core::AtResultCode;

/// 调制解调器操作结果类型
pub type ModemResult<T> = Result<T, ModemError>;

/// 通用调制解调器错误类型
#[derive(Error, Debug, Clone, PartialEq)]
pub enum ModemError {
    /// IO错误
    #[error("IO error: {0}")]
    Io(String),
    
    /// 串口错误
    #[error("Serial port error: {0}")]
    Serial(String),
    
    /// 协议错误
    #[error("Protocol error: {0}")]
    Protocol(String),
    
    /// 命令执行失败
    #[error("Command failed: {0:?}")]
    CommandFailed(AtResultCode),
    
    /// 类型不匹配
    #[error("Type mismatch - expected different type")]
    TypeMismatch,
    
    /// 响应未找到
    #[error("Expected response not found")]
    ResponseNotFound,
    
    /// 解析错误
    #[error("Parse error: {0}")]
    ParseError(String),
    
    /// PDU格式无效
    #[error("Invalid PDU: {0}")]
    InvalidPdu(String),
    
    /// 编码错误
    #[error("Encoding error: {0}")]
    Encoding(String),
    
    /// Future已死亡
    #[error("Future died unexpectedly")]
    FutureDied,
    
    /// 超时
    #[error("Operation timed out")]
    Timeout,
    
    /// 不支持的操作
    #[error("Unsupported operation: {0}")]
    Unsupported(String),
    
    /// 配置错误
    #[error("Configuration error: {0}")]
    Configuration(String),
    
    /// 其他错误
    #[error("Other error: {0}")]
    Other(String),
}

impl From<std::io::Error> for ModemError {
    fn from(err: std::io::Error) -> Self {
        ModemError::Io(err.to_string())
    }
}

impl From<tokio_serial::Error> for ModemError {
    fn from(err: tokio_serial::Error) -> Self {
        ModemError::Serial(err.to_string())
    }
}
impl From<nom::Err<nom::error::Error<&[u8]>>> for ModemError {
    fn from(e: nom::Err<nom::error::Error<&[u8]>>) -> ModemError {
        ModemError::ParseError(format!("{:?}", e))
    }
}
impl From<nom::Err<nom::error::Error<&str>>> for ModemError {
    fn from(e: nom::Err<nom::error::Error<&str>>) -> ModemError {
        ModemError::ParseError(format!("{:?}", e))
    }
}