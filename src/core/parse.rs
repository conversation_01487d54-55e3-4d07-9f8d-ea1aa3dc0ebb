use crate::core::*;
use encoding::all::ASCII;
use encoding::{DecoderTrap, Encoding};
use nom::character::complete::multispace1;

use nom::{
    branch::alt,
    bytes::complete::{tag, take_until, take_till},
    character::complete::{ one_of, not_line_ending},
    combinator::{map, map_res, opt, value},
    multi::{count, many0, many1},
    sequence::{delimited, preceded},
    IResult, Parser,
};
use std::char::{decode_utf16, REPLACEMENT_CHARACTER};

pub fn parse_string(input: &[u8]) -> IResult<&[u8], String> {
    map_res(delimited(tag("\""), take_until("\""), tag("\"")), |data| {
        ASCII.decode(data, DecoderTrap::Replace)
    })
    .parse(input)
}

pub fn parse_ucs2_string(input: &[u8]) -> IResult<&[u8], String> {
    map(
        delimited(
            tag("\""),
            many0(map_res(
                count(one_of("0123456789ABCDEF"), 4),
                |data: Vec<char>| {
                    let st: String = data.into_iter().collect();
                    u16::from_str_radix(&st, 16)
                },
            )),
            tag("\""),
        ),
        |data: Vec<u16>| {
            decode_utf16(data.into_iter())
                .map(|r| r.unwrap_or(REPLACEMENT_CHARACTER))
                .collect::<String>()
        },
    )
    .parse(input)
}

pub fn parse_integer(input: &[u8]) -> IResult<&[u8], u32> {
    // 使用前瞻检查，确保数字后面不是点号，避免将IP地址的第一部分误解析为整数
    let (remaining, digits) = many1(one_of("0123456789")).parse(input)?;

    // 检查数字后面是否跟着点号，如果是则拒绝解析为整数
    if !remaining.is_empty() && remaining[0] == b'.' {
        return Err(nom::Err::Error(nom::error::Error::new(input, nom::error::ErrorKind::Tag)));
    }

    let st: String = digits.into_iter().collect();
    match st.parse() {
        Ok(num) => Ok((remaining, num)),
        Err(_) => Err(nom::Err::Error(nom::error::Error::new(input, nom::error::ErrorKind::MapRes))),
    }
}

pub fn parse_range(input: &[u8]) -> IResult<&[u8], (u32, u32)> {
    (parse_integer, tag("-"), parse_integer)
        .parse(input)
        .map(|(rest, (i1, _, i2))| (rest, (i1, i2)))
}

pub fn parse_unknown(input: &[u8]) -> IResult<&[u8], String> {
    // println!("parsing unknown from: {:?}", std::str::from_utf8(input).unwrap_or("<invalid utf8>"));
    map(many1(nom::character::complete::none_of(",)")), |data| {
        data.into_iter().collect()
    })
    .parse(input)
}

pub fn parse_value(input: &[u8]) -> IResult<&[u8], AtValue> {
    map(
        (
            parse_single_value,
            many0(preceded(tag(","), parse_single_value)),
        ),
        |(first, others)| {
            if others.is_empty() {
                first
            } else {
                let mut ret = vec![first];
                ret.extend(others);
                AtValue::Array(ret)
            }
        },
    )
    .parse(input)
}

pub fn parse_bracketed_array(input: &[u8]) -> IResult<&[u8], AtValue> {
    // println!("parsing bracketed array from: {:?}", std::str::from_utf8(input).unwrap_or("<invalid utf8>"));
    map(delimited(tag("("), parse_value, tag(")")), |v| match v {
        AtValue::Array(ret) => AtValue::BracketedArray(ret),
        AtValue::Empty => AtValue::BracketedArray(vec![]),
        x => AtValue::BracketedArray(vec![x]),
    }).parse(input)
}

pub fn parse_empty(input: &[u8]) -> IResult<&[u8], ()> {
    // println!("parsing empty from: {:?}", std::str::from_utf8(input).unwrap_or("<invalid utf8>"));
    value((), tag("")).parse(input)
}

pub fn parse_single_value(input: &[u8]) -> IResult<&[u8], AtValue> {
    alt((
        parse_bracketed_array,
        map(parse_string, |s| AtValue::String(s)),
        map(parse_range, |x| AtValue::Range(x)),
        map(parse_integer, |i| AtValue::Integer(i)),
        map(parse_unknown, |u| AtValue::Unknown(u)),
        map(parse_empty, |_| AtValue::Empty),
    ))
    .parse(input)
}

pub fn parse_information_response(input: &[u8]) -> IResult<&[u8], (String, AtValue)> {
    map(
        (take_until(":"), tag(":"), opt(tag(" ")), parse_value),
        |(param, _, _, response)| (std::str::from_utf8(param).unwrap().into(), response),
    )
    .parse(input)
}

pub fn parse_response_code(input: &[u8]) -> IResult<&[u8], AtResultCode> {
    alt((
        map(tag("OK"), |_| AtResultCode::Ok),
        map(tag("CONNECT"), |_| AtResultCode::Connect),
        map(tag("RING"), |_| AtResultCode::Ring),
        map(tag("NO CARRIER"), |_| AtResultCode::NoCarrier),
        map(tag("ERROR"), |_| AtResultCode::Error),
        map(tag("NO DIALTONE"), |_| AtResultCode::NoDialtone),
        map(tag("BUSY"), |_| AtResultCode::Busy),
        map(tag("NO ANSWER"), |_| AtResultCode::NoAnswer),
        map(tag("COMMAND NOT SUPPORT"), |_| {
            AtResultCode::CommandNotSupported
        }),
        map(tag("TOO MANY PARAMETERS"), |_| {
            AtResultCode::TooManyParameters
        }),
        map_res(parse_information_response, |(p, r)| {
            if p == "+CME ERROR" {
                if let AtValue::Integer(r) = r {
                    return Ok(AtResultCode::CmeError(r));
                }
            }
            if p == "+CMS ERROR" {
                return Err("CMS ERROR");
            }
            Err("Incorrect information response")
        }),
    ))
    .parse(input)
}



pub fn parse_response_line(input: &[u8]) -> IResult<&[u8], AtResponse> {
    // println!("parsing response line from: {:?}", std::str::from_utf8(input).unwrap_or("<invalid utf8>"));
    alt((
        map(parse_response_code, |c| AtResponse::ResultCode(c)),
        map(parse_information_response, |(p, r)| {
            AtResponse::InformationResponse {
                param: p,
                response: r,
            }
        }),
        map_res(not_line_ending, |s| {
            let st = std::str::from_utf8(s).map_err(|_| ())?.trim();
            if st.is_empty() {
                return Err(());
            }
            Ok(AtResponse::Unknown(st.to_string()))
        }),
    ))
    .parse(input)
}

pub fn responses(input: &[u8]) -> IResult<&[u8], Vec<AtResponse>> {
    // 灵活行结束符解析器
/*     let flexible_line_ending = || alt((
        tag("\r\r\n"),
        tag("\r\n"), 
        tag("\n"),
        tag("\r"),
    )); */

    let result = map(
        many0(
            preceded(
            multispace1,
            opt(
               take_till(|c| c == b'\r' || c == b'\n').and_then(|line_content| {
                parse_response_line(line_content).map(|(_, resp)| (b"" as &[u8], resp))
               })
            )
        )),
        |res| {
            let filtered: Vec<AtResponse> = res.into_iter().filter_map(|s| s).collect();
            log::trace!("parsed {} responses", filtered.len());
            for (i, resp) in filtered.iter().enumerate() {
                log::trace!("  response {}: {:?}", i, resp);
            }
            filtered
        },
    )
    .parse(input);
    log::trace!("result: {:?}", result);
    // 如果解析成功但没有消费所有数据，并且没有解析出任何响应，返回Incomplete
    if let Ok((remaining, responses)) = &result {
        if !remaining.is_empty() && responses.is_empty() {
            log::trace!("no responses parsed but data remaining, returning Incomplete");
            return Err(nom::Err::Incomplete(nom::Needed::Unknown));
        }
    }

    match &result {
        Ok((remaining, responses)) => {
            log::trace!("parse successful, {} bytes remaining", remaining.len());
        }
        Err(e) => {
            log::trace!("parse failed: {:?}", e);
        }
    }

    result
}

#[cfg(test)]
mod test {
    use super::*;
    use AtValue::*;

    #[test]
    fn value_string() {
        assert_eq!(
            parse_string(b"\"testing\"").unwrap(),
            (&[] as &[_], "testing".into())
        );
        assert_eq!(
            parse_value(b"\"testing\"").unwrap(),
            (&[] as &[_], AtValue::String("testing".into()))
        );
    }

    #[test]
    fn value_integer() {
        assert_eq!(parse_integer(b"9001").unwrap(), (&[] as &[_], 9001));
        assert_eq!(
            parse_value(b"9001").unwrap(),
            (&[] as &[_], AtValue::Integer(9001))
        );
    }

    #[test]
    fn value_range() {
        assert_eq!(parse_range(b"2-9001").unwrap(), (&[] as &[_], (2, 9001)));
        assert_eq!(
            parse_value(b"2-9001").unwrap(),
            (&[] as &[_], AtValue::Range((2, 9001)))
        );
    }

    #[test]
    fn value_empty() {
        assert_eq!(parse_empty(b"").unwrap(), (&[] as &[_], ()));
        assert_eq!(parse_value(b"").unwrap(), (&[] as &[_], AtValue::Empty));
        // assert_eq!(multispace0::<&[u8], nom::error::Error<&[u8]>>().parse(b"").unwrap(), (&[] as &[_], &[] as &[u8]));
        // assert_eq!(preceded(multispace0(), tag("")).parse(b"").unwrap(), (&[] as &[_], &[] as &[u8]));
    }
    #[test]
    fn value_bracketed_array() {
        assert_eq!(
            parse_value(b"()").unwrap(),
            (&[] as &[_], AtValue::BracketedArray(vec![]))
        );
        assert_eq!(
            parse_value(b"(1,2,3)").unwrap(),
            (
                &[] as &[_],
                AtValue::BracketedArray(vec![
                    AtValue::Integer(1),
                    AtValue::Integer(2),
                    AtValue::Integer(3)
                ])
            )
        );
        assert_eq!(
            parse_value(b"((1,2,3),(4,5,6))").unwrap(),
            (
                &[] as &[_],
                AtValue::BracketedArray(vec![
                    AtValue::BracketedArray(vec![
                        AtValue::Integer(1),
                        AtValue::Integer(2),
                        AtValue::Integer(3)
                    ]),
                    AtValue::BracketedArray(vec![
                        AtValue::Integer(4),
                        AtValue::Integer(5),
                        AtValue::Integer(6)
                    ])
                ])
            )
        );
        assert_eq!(
            parse_value(b"((1,2,3),(4,5,6),)").unwrap(),
            (
                &[] as &[_],
                AtValue::BracketedArray(vec![
                    AtValue::BracketedArray(vec![
                        AtValue::Integer(1),
                        AtValue::Integer(2),
                        AtValue::Integer(3)
                    ]),
                    AtValue::BracketedArray(vec![
                        AtValue::Integer(4),
                        AtValue::Integer(5),
                        AtValue::Integer(6)
                    ]),
                    AtValue::Empty
                ])
            )
        );
    }
    #[test]
    fn value_unknown() {
        assert_eq!(
            parse_unknown(b"invalid").unwrap(),
            (&[] as &[_], "invalid".into())
        );
        assert_eq!(
            parse_value(b"invalid").unwrap(),
            (&[] as &[_], AtValue::Unknown("invalid".into()))
        );
        assert_eq!(parse_value(b"").unwrap(), (&[] as &[_], AtValue::Empty));
        assert_eq!(parse_value(b"\"()\"").unwrap(), (&[] as &[_], AtValue::String("()".into())));
        assert_eq!(parse_value(b"\"\"").unwrap(), (&[] as &[_], AtValue::String("".into())));
    }

    #[test]
    fn value_complex() {
        assert_eq!(
            parse_value(b"3,0,15,\"GSM\",(),(0-3),,(0-1),invalid,(0-2,15),(\"GSM\",\"IRA\")")
                .unwrap(),
            (
                &[] as &[_],
                Array(vec![
                    Integer(3),
                    Integer(0),
                    Integer(15),
                    String("GSM".into()),
                    BracketedArray(vec![]),
                    BracketedArray(vec![Range((0, 3))]),
                    Empty,
                    BracketedArray(vec![Range((0, 1))]),
                    Unknown("invalid".into()),
                    BracketedArray(vec![Range((0, 2)), Integer(15)]),
                    BracketedArray(vec![String("GSM".into()), String("IRA".into()),])
                ])
            )
        )
    }
    #[test]
    fn test_not_line_ending() {
        assert_eq!(
            not_line_ending::<&[u8], nom::error::Error<&[u8]>>(&b"GOSUNCNWELINK\r\n"[..]).unwrap(),
            (&b"\r\n"[..], &b"GOSUNCNWELINK"[..])
        );
        assert_eq!(
            not_line_ending::<&[u8], nom::error::Error<&[u8]>>(&b"\r\nGOSUNCNWELINK"[..]).unwrap(),
            (&b"\r\nGOSUNCNWELINK"[..], &b""[..])
        );
        assert_eq!(
            not_line_ending::<&[u8], nom::error::Error<&[u8]>>(&b""[..]).unwrap(),
            (&b""[..], &b""[..])
        );
    }
    #[test]
    fn value_multiple_lines() {
        assert_eq!(
            responses(b"\r\nGOSUNCNWELINK\r\n\r\nOK\r\n").unwrap(),
            (&[] as &[_], vec![AtResponse::Unknown("GOSUNCNWELINK".into()), AtResponse::ResultCode(AtResultCode::Ok)])
        );
        assert_eq!(
            responses(b"\r\n+ZECMCALL: IPV4, *************, *************, ************, **********\r\n\r\nOK\r\n").unwrap(),
            (&[] as &[_], vec![
                AtResponse::InformationResponse { 
                    param: "+ZECMCALL".to_string(),
                    response: AtValue::Array(vec![
                        AtValue::Unknown("IPV4".to_string()),
                        AtValue::Unknown(" *************".to_string()),
                        AtValue::Unknown(" *************".to_string()),
                        AtValue::Unknown(" ************".to_string()),
                        AtValue::Unknown(" **********".to_string()),
                    ])
                },
                AtResponse::ResultCode(AtResultCode::Ok)
            ])
        );
    }
    #[test]
    fn value_empty_response() {
        assert_eq!(
            responses(b"").unwrap(),
            (&[] as &[_], vec![])
        );
    }

    #[test]
    fn value_double_cr_line_ending() {
        // 测试基本的双回车情况
        assert_eq!(
            responses(b"\r\r\nOK\r\n").unwrap(),
            (&[] as &[_], vec![AtResponse::ResultCode(AtResultCode::Ok)])
        );
        assert_eq!(
            responses(b"\r\nOK\r\r\nOK\r\n").unwrap(),
            (&[] as &[_], vec![AtResponse::ResultCode(AtResultCode::Ok), AtResponse::ResultCode(AtResultCode::Ok)])
        );
    }

    #[test]
    fn test_ip_address_parsing() {
        // 测试IP地址不会被误解析为整数
        assert_eq!(
            parse_value(b"************").unwrap(),
            (&[] as &[_], AtValue::Unknown("************".to_string()))
        );

        // 测试CGPADDR响应的解析
        assert_eq!(
            parse_information_response(b"+CGPADDR: 1,************").unwrap(),
            (&[] as &[_], ("+CGPADDR".to_string(), AtValue::Array(vec![
                AtValue::Integer(1),
                AtValue::Unknown("************".to_string())
            ])))
        );

        // 测试纯整数仍然能正确解析
        assert_eq!(
            parse_value(b"123").unwrap(),
            (&[] as &[_], AtValue::Integer(123))
        );

        // 测试整数后跟逗号的情况
        assert_eq!(
            parse_value(b"1,************").unwrap(),
            (&[] as &[_], AtValue::Array(vec![
                AtValue::Integer(1),
                AtValue::Unknown("************".to_string())
            ]))
        );
    }

    #[test]
    fn test_cgpaddr_response_parsing() {
        // 测试原始问题：+CGPADDR: 1,************ 应该解析为 [Integer(1), Unknown("************")]
        // 而不是 [Integer(1), Integer(10)]
        let response = b"\r\n+CGPADDR: 1,************\r\n\r\nOK\r\n";
        let result = responses(response).unwrap();

        assert_eq!(result.0, &[] as &[u8]);
        assert_eq!(result.1.len(), 2);

        match &result.1[0] {
            AtResponse::InformationResponse { param, response } => {
                assert_eq!(param, "+CGPADDR");
                match response {
                    AtValue::Array(values) => {
                        assert_eq!(values.len(), 2);
                        assert_eq!(values[0], AtValue::Integer(1));
                        assert_eq!(values[1], AtValue::Unknown("************".to_string()));
                    }
                    _ => panic!("Expected Array, got {:?}", response),
                }
            }
            _ => panic!("Expected InformationResponse, got {:?}", result.1[0]),
        }

        assert_eq!(result.1[1], AtResponse::ResultCode(AtResultCode::Ok));
    }
}
