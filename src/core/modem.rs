//! 模组管理器
//!
//! 实现完整的网络连接流程，包括模块检测、配置、拨号和状态监控

use crate::core::{
    AtCommand, AtResponse, AtResponsePacket, AtValue, ConfigManager, ConnectionState, Modem, ModemError, ModemInterface, ModemResult,
    NetModem, RegistrationState, StateManager,
};
use crate::{CommandHandler, VendorConfig};
use std::time::{Duration, Instant};
use tokio::time::{sleep, timeout};
use log::{trace, info, debug, warn, error};
use tokio::sync::Mutex;


/// 重启类型
#[derive(Debug, Clone, Copy)]
pub enum RestartType {
    /// 软重启 (AT+CFUN=1,1)
    Soft,
    /// 硬重启 (断电重启)
    Hard,
}

pub struct RestartState {
    restart_count: u32,
    last_restart_time: Option<Instant>,
    restart_type: RestartType,
    restart_interval: Duration,
}
impl Default for RestartState {
    fn default() -> Self {
        Self {
            restart_count: 0,
            last_restart_time: None,
            restart_type: RestartType::Soft,
            restart_interval: Duration::from_secs(30),
        }
    }
}

/// 模组管理器
pub struct ModemManager<T>
where
    T: VendorConfig,
{
    modem: Option<NetModem>,
    device_path: String,
    config_manager: ConfigManager,
    state_manager: StateManager,
    vendor_config: T,
    restart_state: Mutex<RestartState>,
}

impl<T> ModemManager<T>
where
    T: VendorConfig,
{
    /// 创建新的模组管理器
    pub fn new(
        modem: NetModem,
        config_manager: ConfigManager,
        state_manager: StateManager,
        vendor_config: T,
    ) -> Self {
        let config = config_manager.get_config();
        let device_path = config.serial_config.device_path.clone();
        Self {
            modem: Some(modem),
            device_path,
            config_manager,
            state_manager,
            vendor_config,
            restart_state: Mutex::new(RestartState::default()),
        }
    }

    /// 获取 modem 的可变引用，如果不存在则返回错误
    fn get_modem_mut(&mut self) -> ModemResult<&mut NetModem> {
        self.modem.as_mut().ok_or_else(|| ModemError::Other("Modem 连接已断开".to_string()))
    }

    /// 公共方法：执行 AT 命令
    pub async fn execute_at_command(&self, cmd: String, timeout: Duration) -> ModemResult<String> {
        let modem = self.get_modem()?;
        let at_cmd = AtCommand::Text {
            text: cmd,
            expected: vec![],
        };

        match tokio::time::timeout(timeout, modem.send_raw(at_cmd)).await {
            Ok(Ok(packet)) => {
                // 将 AtResponsePacket 转换为字符串
                let mut result = String::new();
                for response in &packet.responses {
                    match response {
                        AtResponse::InformationResponse { param, response } => {
                            result.push_str(&format!("{}: {:?}\n", param, response));
                        }
                        AtResponse::Unknown(text) => {
                            result.push_str(&format!("{}\n", text));
                        }
                        _ => {
                            result.push_str(&format!("{:?}\n", response));
                        }
                    }
                }
                result.push_str(&format!("{:?}", packet.status));
                Ok(result)
            }
            Ok(Err(e)) => Err(e),
            Err(_) => Err(ModemError::Timeout),
        }
    }

    /// 获取 modem 的引用，如果不存在则返回错误
    fn get_modem(&self) -> ModemResult<&NetModem> {
        self.modem.as_ref().ok_or_else(|| ModemError::Other("Modem 连接已断开".to_string()))
    }

    /// 释放当前的 modem 连接
    async fn release_modem(&mut self) {
        if self.modem.is_some() {
            log::info!("释放 modem 连接");
            self.modem = None;
        }
    }

    /// 重新创建 modem 连接
    pub async fn recreate_modem(&mut self) -> ModemResult<()> {
        log::info!("重新创建 modem 连接到 {}", self.device_path);
        
        let stream = self.config_manager.get_config().get_serial_config().open_stream()?;
        let modem = NetModem::new_from_stream(stream).await?;
        self.modem = Some(modem);

        log::info!("Modem 连接重新创建成功");
        Ok(())
    }

    /// 带重试的命令执行器
    pub async fn execute_with_retry<R: 'static>(
        &mut self,
        config: &RetryConfig,
        log_content: &str,
        handler_factory: impl Fn() -> CommandHandler<Box<dyn Fn(AtResponsePacket) -> ModemResult<R> + Send + Sync>, R>,
    ) -> ModemResult<R>
    {
        let mut last_error = ModemError::Other("未知错误".to_string());

        for attempt in 1..=config.max_attempts {
            log::info!("{} 执行尝试 {}/{}", log_content, attempt, config.max_attempts);

            // 每次重新创建 handler 以避免所有权问题
            let handler = handler_factory();

            match timeout(config.timeout_duration, self.get_modem()?.send_with_handler(handler.cmd, handler.handler)).await {
                Ok(Ok(result)) => {
                    if attempt > 1 {
                        log::debug!("{} 在第 {} 次尝试后成功",log_content, attempt);
                    }
                    return Ok(result);
                }
                Ok(Err(e)) => {
                    log::warn!("{} 执行失败 (尝试 {}): {}",log_content, attempt, e);
                    last_error = e;
                }
                Err(_) => {
                    log::warn!("{} 执行超时 (尝试 {})",log_content, attempt);
                    last_error = ModemError::Timeout;
                }
            }

            if attempt < config.max_attempts {
                sleep(Duration::from_millis(500)).await;
            }
        }

        Err(last_error)
    }

    /// 检查设备是否可用 - 处理设备可能短暂消失的情况
    pub async fn check_device_availability(&mut self) -> ModemResult<()> {
        log::debug!("检查设备可用性");

        // 参考脚本中的check_ttyUSB1函数，最多等待300秒
        let max_attempts = 300;
        let mut last_error = ModemError::Other("设备不可用".to_string());

        for attempt in 1..=max_attempts {
            if attempt % 20 == 1 {  // 每60秒记录一次日志
                log::info!("检查设备可用性 执行尝试 {}/{}", attempt, max_attempts);
            }

            // 发送简单的AT命令检查设备响应
            let modem = match self.get_modem() {
                Ok(modem) => modem,
                Err(_) => {
                    log::trace!("Modem 连接不可用");
                    last_error = ModemError::Other("Modem 连接不可用".to_string());
                    if attempt < max_attempts {
                        sleep(Duration::from_secs(1)).await;
                    }
                    let _ = self.recreate_modem().await;
                    continue;
                }
            };

            match timeout(Duration::from_secs(1), modem.send_raw(AtCommand::Text {
                text: "AT".to_string(),
                expected: vec![],
            })).await {
                Ok(Ok(_)) => {
                    if attempt > 1 {
                        log::info!("设备在第 {} 次尝试后恢复可用", attempt);
                    }
                    return Ok(());
                }
                Ok(Err(e)) => {
                    log::trace!("设备暂时不可用: {}", e);
                    last_error = e;
                }
                Err(_) => {
                    log::trace!("设备响应超时");
                    last_error = ModemError::Timeout;
                }
            }

            if attempt < max_attempts {
                sleep(Duration::from_secs(1)).await;
            }
        }

        log::error!("设备在 {} 秒内未恢复可用", max_attempts);
        Err(last_error)
    }

    /// 执行完整的网络连接流程
    pub async fn connect(&mut self) -> ModemResult<()> {
        log::info!("开始网络连接流程");
        
        // 更新连接状态为连接中
        self.state_manager.update_state(|state| {
            state.set_state(ConnectionState::Connecting)
        });

        let mut attempts = 0;
        let max_attempts = 3;

        while attempts < max_attempts {
            attempts += 1;
            log::info!("网络连接尝试 {}/{}", attempts, max_attempts);

            match self.try_connect().await {
                Ok(()) => {
                    log::info!("网络连接成功");
                    self.state_manager.update_state(|state| {
                        state.set_state(ConnectionState::Connected)
                    });
                    self.reset_restart_count().await;
                    return Ok(());
                }
                Err(e) => {
                    log::warn!("网络连接失败 (尝试 {}): {}", attempts, e);

                    // 检查是否是设备不可用的问题
                    if attempts < max_attempts {
                        log::info!("检查设备可用性...");
                        if let Err(device_err) = self.check_device_availability().await {
                            log::error!("设备不可用: {}", device_err);
                            return Err(device_err);
                        }

                        log::info!("设备可用，等待 5 秒后重试...");
                        sleep(Duration::from_secs(5)).await;
                    }
                }
            }
        }

        // 所有尝试都失败，尝试重启模块
        log::error!("网络连接失败");
        return Err(ModemError::Other("网络连接失败".to_string()));
    }

    /// 尝试单次网络连接
    async fn try_connect(&mut self) -> ModemResult<()> {
        // 1. 发送ATI检测模块是否就绪，并更新基础信息
        log::info!("1. 检测模块状态并获取基础信息");
        self.check_module_ready().await?;

        // 2. 查询模块cfun，若不为1需要置1
        log::info!("2. 检查并设置CFUN状态");
        self.ensure_cfun_ready().await?;
        
        // 3. 检查SIM卡状态
        log::info!("3. 检查SIM卡状态");
        self.get_modem()?.check_sim().await?;

        self.state_manager.update_state(|state| {
            state.set_sim("Ready".to_string());
            None
        });


        // 3. 检查csq有信号
        log::info!("3. 检查信号质量");
        self.check_signal_quality().await?;

        // 4. 检查网络注册状态
        log::info!("4. 检查网络注册状态");
        self.check_network_registration().await?;

        // 5. 从配置中获取pdp上下文信息，设置并进行拨号
        log::info!("5. 设置PDP上下文并拨号");
        self.setup_pdp_and_dial().await?;

        log::info!("网络连接流程完成");
        Ok(())
    }

    /// 检测模块是否就绪并更新基础信息
    async fn check_module_ready(&mut self) -> ModemResult<()> {
        log::debug!("发送ATI命令检测模块");

        let config = RetryConfig::fast_command();

        let mut last_error = ModemError::Other("未知错误".to_string());

        for attempt in 1..=config.max_attempts {
            log::info!("ATI命令检测模块 执行尝试 {}/{}", attempt, config.max_attempts);

            match timeout(config.timeout_duration, self.get_modem()?.get_information()).await {
                Ok(Ok(info)) => {
                    log::info!("模块信息获取成功");

                    // 解析ATI响应并更新状态
                    let mut manufacturer = String::new();
                    let mut model = String::new();
                    let mut revision = String::new();
                    let mut imei = String::new();

                    for response in &info.responses {
                        if let AtResponse::InformationResponse { param, response } = response {
                            match param.as_str() {
                                "Manufacturer" => {
                                    if let Ok(value) = response.get_unknown() {
                                        manufacturer = value.clone();
                                        log::info!("制造商: {}", manufacturer);
                                    }
                                }
                                "Model" => {
                                    if let Ok(value) = response.get_unknown() {
                                        model = value.clone();
                                        log::info!("型号: {}", model);
                                    }
                                }
                                "Revision" => {
                                    if let Ok(value) = response.get_unknown() {
                                        revision = value.clone();
                                        log::info!("版本: {}", revision);
                                    }
                                }
                                "IMEI" => {
                                    if let Ok(value) = response.get_unknown() {
                                        imei = value.clone();
                                        log::info!("IMEI: {}", imei);
                                    }
                                }
                                _ => {}
                            }
                        }
                    }

                    // 立即更新状态管理器
                    self.state_manager.update_state(|state| {
                        if !model.is_empty() {
                            state.set_model(model);
                        }
                        if !revision.is_empty() {
                            state.set_revision(revision);
                        }
                        if !imei.is_empty() {
                            state.set_imei(imei);
                        }
                        None
                    });

                    log::info!("模块信息已更新到状态管理器");

                    // 获取网络制式信息
                    let mode_handler = self.vendor_config.mode_command();
                    if let Ok(mode_result) = timeout(Duration::from_secs(2), self.get_modem()?.send_with_handler(mode_handler.cmd, mode_handler.handler)).await {
                        match mode_result {
                            Ok(mode) => {
                                log::info!("当前网络制式: {}", mode);
                                self.state_manager.update_state(|state| {
                                    state.set_mode(mode);
                                    None
                                });
                            }
                            Err(e) => {
                                log::debug!("获取网络制式失败: {}", e);
                            }
                        }
                    }

                    if attempt > 1 {
                        log::debug!("ATI命令检测模块 在第 {} 次尝试后成功", attempt);
                    }
                    return Ok(());
                }
                Ok(Err(e)) => {
                    log::warn!("ATI命令检测模块 执行失败 (尝试 {}): {}", attempt, e);
                    last_error = e;
                }
                Err(_) => {
                    log::warn!("ATI命令检测模块 执行超时 (尝试 {})", attempt);
                    last_error = ModemError::Timeout;
                }
            }

            if attempt < config.max_attempts {
                sleep(Duration::from_millis(500)).await;
            }
        }

        Err(last_error)
    }

    /// 确保CFUN状态为1
    async fn ensure_cfun_ready(&mut self) -> ModemResult<()> {
        log::debug!("检查CFUN状态");

        // 检查当前CFUN状态 - 使用重试机制
        let cfun = self.execute_with_retry(
            &RetryConfig::fast_command(),
            "获取CFUN状态",
            || CommandHandler {
                cmd: AtCommand::Read {
                    param: "+CFUN".to_string(),
                },
                handler: Box::new(|packet| {
                    let val = packet.extract_named_response("+CFUN")?;
                    Ok(*val.get_integer()?)
                }),
            },
        ).await?;

        if cfun != 1 {
            log::info!("CFUN状态为 {}，设置为 1", cfun);

            // 设置CFUN为1 - 使用重试机制
            self.execute_with_retry(
                &RetryConfig::slow_command(),
                "设置CFUN状态",
                || CommandHandler {
                    cmd: AtCommand::Equals {
                        param: "+CFUN".to_string(),
                        value: AtValue::Integer(1),
                    },
                    handler: Box::new(|packet| packet.assert_ok()),
                },
            ).await?;

            log::info!("CFUN设置成功");

            // 等待模块重新初始化
            log::info!("等待模块重新初始化...");
            sleep(Duration::from_secs(10)).await;
        } else {
            log::debug!("CFUN状态正常");
        }

        Ok(())
    }

    /// 检查信号质量 - 使用csq
    async fn check_signal_quality(&mut self) -> ModemResult<()> {
        debug!("检查信号质量");

        let config = RetryConfig::signal_check();
        let mut last_error = ModemError::Other("未知错误".to_string());

        for attempt in 1..=config.max_attempts {
            info!("检查信号质量 执行尝试 {}/{}", attempt, config.max_attempts);

            let signal_handler = self.vendor_config.signal_quality_command();

            match timeout(config.timeout_duration, self.get_modem()?.send_with_handler(signal_handler.cmd, signal_handler.handler)).await {
                Ok(Ok(signal_quality)) => {
                    if attempt > 1 {
                        debug!("检查信号质量 在第 {} 次尝试后成功", attempt);
                    }

                    info!("信号质量: RSRP={} dBm, SINR={}", signal_quality.rsrp, signal_quality.sinr);

                    // 更新状态管理器中的信号质量
                    self.state_manager.update_state(|state| {
                        state.set_signal_quality(signal_quality.rsrp, signal_quality.sinr);
                        None
                    });

                    return Ok(());
                }
                Ok(Err(e)) => {
                    warn!("检查信号质量 执行失败 (尝试 {}): {}", attempt, e);
                    last_error = e;
                }
                Err(_) => {
                    warn!("检查信号质量 执行超时 (尝试 {})", attempt);
                    last_error = ModemError::Timeout;
                }
            }

            if attempt < config.max_attempts {
                sleep(Duration::from_millis(500)).await;
            }
        }

        Err(last_error)
    }

    /// 检查网络注册状态
    async fn check_network_registration(&mut self) -> ModemResult<()> {
        log::debug!("检查网络注册状态");

        let config = RetryConfig::network_registration();

        self.execute_with_retry(
            &config,
            "检查网络注册状态",
            || CommandHandler {
                cmd: AtCommand::Read {
                    param: "+CREG".to_string(),
                },
                handler: Box::new(|packet| {
                    let val = packet.extract_named_response("+CREG")?;
                    let reg_state = RegistrationState::from_value(
                        *val.get_array()?
                            .get(1)
                            .ok_or(ModemError::ParseError(format!(
                                "failed to get registration state from {}",
                                val
                            )))?
                            .get_integer()?,
                    );

                    match reg_state {
                        RegistrationState::Registered => {
                            log::info!("网络注册成功");
                            Ok(())
                        }
                        RegistrationState::Searching => {
                            log::debug!("正在搜索网络...");
                            Err(ModemError::Other("正在搜索网络".to_string()))
                        }
                        RegistrationState::Denied => {
                            warn!("网络注册被拒绝");
                            Err(ModemError::Other("网络注册被拒绝".to_string()))
                        }
                        _ => {
                            log::warn!("网络注册状态: {:?}", reg_state.as_str());
                            Err(ModemError::Other(format!("网络注册状态: {:?}", reg_state.as_str())))
                        }
                    }
                }),
            },
        ).await
    }

    /// 设置PDP上下文并拨号
    async fn setup_pdp_and_dial(&mut self) -> ModemResult<()> {
        log::debug!("设置PDP上下文");

        let config = self.config_manager.get_config();
        let pdp_context = config.get_pdp_context();

        log::info!("PDP上下文配置: 类型={}, APN={}",
                  pdp_context.pdp_type, pdp_context.apn);

        // 1. 设置PDP上下文 - 使用厂商特定命令
        log::debug!("设置PDP上下文参数");
        let setup_config = RetryConfig::slow_command();
        let setup_cmd = self.vendor_config.setup_pdp_context_command(pdp_context);

        self.execute_with_retry(
            &setup_config,
            "设置PDP上下文",
            || CommandHandler {
                cmd: setup_cmd.cmd.clone(),
                handler: Box::new(|packet| packet.assert_ok()),
            },
        ).await?;

        log::info!("PDP上下文设置成功");

        // 2. 拨号
        log::debug!("拨号");
        let dial_config = RetryConfig::dial_operation();
        let dial_cmd = self.vendor_config.connect_command(None);

        self.execute_with_retry(
            &dial_config,
            "拨号操作",
            || CommandHandler {
                cmd: dial_cmd.cmd.clone(),
                handler: Box::new(|packet| {
                    packet.assert_ok()?;
                    Ok(true)
                }),
            },
        ).await?;

        log::info!("拨号成功");
        
        log::info!("PDP上下文设置和拨号完成");
        Ok(())
    }

    /// 定期检查并更新模块状态
    pub async fn monitor_status(&mut self) -> ModemResult<()> {
        log::debug!("开始状态监控");

        // 首先检查设备是否可用 - 发送简单的AT命令
        let modem = match self.get_modem() {
            Ok(modem) => modem,
            Err(e) => {
                log::warn!("Modem 连接不可用: {}", e);
                return Err(ModemError::Other("Modem 连接不可用".to_string()));
            }
        };

        match timeout(Duration::from_secs(1), modem.send_raw(AtCommand::Text {
            text: "AT".to_string(),
            expected: vec![],
        })).await {
            Ok(Ok(_)) => {
                trace!("设备响应正常");
            }
            Ok(Err(e)) => {
                warn!("设备响应异常: {}", e);
                return Err(ModemError::Other("设备不可用".to_string()));
            }
            Err(_) => {
                warn!("设备响应超时，可能设备暂时不可用");
                return Err(ModemError::Timeout);
            }
        }

        let mut has_error = false;

        // 检查信号质量 - 使用厂商特定的命令
        let signal_handler = self.vendor_config.signal_quality_command();
        match timeout(Duration::from_secs(2), self.get_modem()?.send_with_handler(signal_handler.cmd, signal_handler.handler)).await {
            Ok(Ok(signal_quality)) => {
                debug!("当前信号质量: RSRP={} dBm, SINR={}", signal_quality.rsrp, signal_quality.sinr);
                // 更新状态管理器中的信号质量
                self.state_manager.update_state(|state| {
                    state.set_signal_quality(signal_quality.rsrp, signal_quality.sinr);
                    None
                });
            }
            Ok(Err(e)) => {
                warn!("获取信号质量失败: {}", e);
                has_error = true;
            }
            Err(_) => {
                warn!("获取信号质量超时");
                has_error = true;
            }
        }

        // 检查网络注册状态
        match timeout(Duration::from_secs(2), self.get_modem()?.get_registration_state()).await {
            Ok(Ok(reg_state)) => {
                debug!("当前网络注册状态: {:?}", reg_state);
                // 更新状态管理器中的注册状态
                self.state_manager.update_state(|state| {
                    state.set_registration_state(reg_state.clone());
                    None
                });

                // 如果网络断开，标记需要重新连接
                if !matches!(reg_state, RegistrationState::Registered) {
                    warn!("网络连接丢失，状态: {:?}", reg_state);
                    self.state_manager.update_state(|state| {
                        state.set_state(ConnectionState::Disconnected)
                    });
                    has_error = true;
                }
            }
            Ok(Err(e)) => {
                warn!("获取网络注册状态失败: {}", e);
                has_error = true;
            }
            Err(_) => {
                warn!("获取网络注册状态超时");
                has_error = true;
            }
        }

        // 检查运营商信息 - 这个不是关键信息，失败不影响整体状态
        if let Ok(operator_result) = timeout(Duration::from_secs(2), self.get_modem()?.get_operator()).await {
            match operator_result {
                Ok(operator) => {
                    log::debug!("当前运营商: {}", operator);
                    self.state_manager.update_state(|state| {
                        state.set_oper(operator);
                        None
                    });
                }
                Err(e) => {
                    log::debug!("获取运营商信息失败: {}", e);
                }
            }
        }

        // 检查网络制式 - 使用厂商特定的命令
        let mode_handler = self.vendor_config.mode_command();
        if let Ok(mode_result) = timeout(Duration::from_secs(2), self.get_modem()?.send_with_handler(mode_handler.cmd, mode_handler.handler)).await {
            match mode_result {
                Ok(mode) => {
                    log::debug!("当前网络制式: {}", mode);
                    self.state_manager.update_state(|state| {
                        state.set_mode(mode);
                        None
                    });
                }
                Err(e) => {
                    log::debug!("获取网络制式失败: {}", e);
                }
            }
        }

        // 如果有关键错误，返回错误以触发重连
        if has_error {
            Err(ModemError::Other("状态监控检测到关键错误".to_string()))
        } else {
            Ok(())
        }
    }

    /// 启动定期状态监控
    pub async fn start_periodic_monitoring(&mut self, interval: Duration) -> ModemResult<()> {
        log::info!("启动定期状态监控，间隔: {:?}", interval);

        loop {
            if let Err(e) = self.monitor_status().await {
                log::error!("状态监控失败: {}", e);

                // 如果监控失败，可能是连接问题，尝试重新连接
                if let Err(reconnect_err) = self.connect().await {
                    log::error!("重新连接失败: {}", reconnect_err);
                }
            }

            sleep(interval).await;
        }
    }

    /// 生成诊断信息
    pub async fn generate_diagnostic_info(&self) -> String {
        let state = self.state_manager.get_state();
        let config = self.config_manager.get_config();

        format!(
            "网络模块诊断信息:\n\
            - 厂商配置: {}\n\
            - 模块型号: {}\n\
            - 模块版本: {}\n\
            - IMEI: {}\n\
            - SIM卡状态: {}\n\
            - 运营商: {}\n\
            - 网络制式: {}\n\
            - 信号强度(RSRP): {} dBm\n\
            - 信噪比(SINR): {}\n\
            - 连接状态: {}\n\
            - 重启次数: {}\n\
            - PDP配置: 类型={}, APN={}\n\
            - 串口设备: {}\n\
            - 波特率: {}",
            self.get_vendor_info(),
            state.model(),
            state.revision(),
            state.imei(),
            state.sim(),
            state.oper(),
            state.mode(),
            state.rsrp(),
            state.sinr(),
            state.state(),
            self.get_restart_count().await,
            config.get_pdp_context().pdp_type,
            config.get_pdp_context().apn,
            config.get_serial_config().device_path,
            config.get_serial_config().baud_rate
        )
    }

    /// 重启接口 - 根据重启类型执行不同重启操作
    pub async fn restart(&mut self, restart_type: RestartType) -> ModemResult<()> {
        // 检查重启间隔
        {
            let mut restart_state = self.restart_state.lock().await;
            if let Some(last_restart) = restart_state.last_restart_time {
                let elapsed = last_restart.elapsed();
                if elapsed < restart_state.restart_interval {
                    let wait_time = restart_state.restart_interval - elapsed;
                    log::info!("距离上次重启时间过短，等待 {:?}", wait_time);
                    sleep(wait_time).await;
                }
            }

            restart_state.restart_count += 1;
            restart_state.last_restart_time = Some(Instant::now());
            restart_state.restart_type = restart_type;

            log::info!("开始重启模块 (第 {} 次, 类型: {:?})", restart_state.restart_count, restart_type);
        }

        // 更新状态
        self.state_manager.update_state(|state| {
            state.set_state(ConnectionState::Disconnecting)
        });

        // 根据重启类型执行不同操作
        match restart_type {
            RestartType::Soft => {
                log::info!("执行软重启");
                self.soft_restart().await?;
            }
            RestartType::Hard => {
                log::info!("执行硬重启");
                self.hard_restart().await?;
            }
        }

        Ok(())
    }

    /// 内部重启模块 - 智能选择重启类型
    pub async fn restart_module(&mut self) -> ModemResult<()> {
        // 根据连接成功状态和重启次数选择重启类型
        let restart_type =
            if self.get_restart_count().await < 2 {
                RestartType::Soft
            } else {
                // 软重启失败后使用硬重启
                RestartType::Hard
            };

        self.restart(restart_type).await
    }

    /// 软重启 - 使用厂商特定命令
    async fn soft_restart(&mut self) -> ModemResult<()> {
        info!("执行软重启命令");

        let restart_handler = self.vendor_config.factory_reset_command();

        let result = match timeout(
            Duration::from_secs(10),
            self.get_modem()?.send_with_handler(restart_handler.cmd, restart_handler.handler)
        ).await {
            Ok(Ok(_)) => {
                info!("软重启命令发送成功");
                Ok(())
            }
            Ok(Err(e)) => {
                error!("软重启命令失败: {}", e);
                Err(e)
            }
            Err(_) => {
                error!("软重启命令超时");
                Err(ModemError::Timeout)
            }
        };

        // 释放接口，避免设备枚举变为/dev/ttyUSB2
        self.release_modem().await;

        result
    }

    /// 硬重启 - 使用厂商特定命令
    async fn hard_restart(&mut self) -> ModemResult<()> {
        log::info!("执行硬重启命令");

        let restart_handler = self.vendor_config.reset_command();

        let result = match timeout(
            Duration::from_secs(15),
            self.get_modem()?.send_with_handler(restart_handler.cmd, restart_handler.handler)
        ).await {
            Ok(Ok(_)) => {
                log::info!("硬重启命令发送成功");
                Ok(())
            }
            Ok(Err(e)) => {
                log::error!("硬重启命令失败: {}", e);
                Err(e)
            }
            Err(_) => {
                log::error!("硬重启命令超时");
                Err(ModemError::Timeout)
            }
        };

        // 释放接口，避免设备枚举变为/dev/ttyUSB2
        self.release_modem().await;

        result
    }

    /// 获取当前连接状态
    pub fn get_connection_state(&self) -> ConnectionState {
        // 从状态管理器获取状态，需要访问内部的ConnectionState
        // 这里需要修改状态管理器的实现来提供正确的访问方法
        // 暂时返回默认状态
        ConnectionState::Disconnected
    }

    /// 获取重启计数
    pub async fn get_restart_count(&self) -> u32 {
        let restart_state = self.restart_state.lock().await;
        restart_state.restart_count
    }

    /// 获取当前重启类型
    pub async fn get_restart_type(&self) -> RestartType {
        let restart_state = self.restart_state.lock().await;
        restart_state.restart_type
    }

    /// 获取设备路径
    pub fn get_device_path(&self) -> &str {
        &self.device_path
    }

    /// 重置重启计数
    pub async fn reset_restart_count(&mut self) {
        let mut restart_state = self.restart_state.lock().await;
        restart_state.restart_count = 0;
        restart_state.last_restart_time = None;
        restart_state.restart_type = RestartType::Soft;
        log::info!("重启计数已重置");
    }

    /// 获取厂商配置信息
    pub fn get_vendor_info(&self) -> String {
        format!(
            "厂商: {}, 支持的模块: {:?}",
            self.vendor_config.vendor_name(),
            self.vendor_config.supported_models()
        )
    }

    /// 简化的重启方法，用于 DBus 接口
    pub async fn restart_hard(&mut self) -> ModemResult<()> {
        self.restart(RestartType::Hard).await
    }

    /// 简化的软重启方法，用于 DBus 接口
    pub async fn restart_soft(&mut self) -> ModemResult<()> {
        self.restart(RestartType::Soft).await
    }
}

/// 通用的重试机制
pub struct RetryConfig {
    pub max_attempts: u32,
    pub timeout_duration: Duration,
}

impl Default for RetryConfig {
    fn default() -> Self {
        Self {
            max_attempts: 3,
            timeout_duration: Duration::from_secs(1), // 参考脚本使用1000ms
        }
    }
}

impl RetryConfig {
    /// 快速AT命令配置（如ATI, AT+CFUN?等）
    pub fn fast_command() -> Self {
        Self {
            max_attempts: 3,
            timeout_duration: Duration::from_secs(1),
        }
    }

    /// 慢速AT命令配置（如AT+CFUN=1等）
    pub fn slow_command() -> Self {
        Self {
            max_attempts: 5,
            timeout_duration: Duration::from_secs(3),
        }
    }

    /// 网络注册检查配置
    pub fn network_registration() -> Self {
        Self {
            max_attempts: 30, // 参考脚本最多等待30次
            timeout_duration: Duration::from_secs(2),
        }
    }

    /// 信号质量检查配置
    pub fn signal_check() -> Self {
        Self {
            max_attempts: 8, // 参考脚本最多8次
            timeout_duration: Duration::from_secs(1),
        }
    }

    /// 拨号操作配置
    pub fn dial_operation() -> Self {
        Self {
            max_attempts: 5,
            timeout_duration: Duration::from_secs(5),
        }
    }

    /// SIM卡检查配置
    pub fn sim_check() -> Self {
        Self {
            max_attempts: 300, // 参考脚本最多300次
            timeout_duration: Duration::from_secs(1),
        }
    }
}