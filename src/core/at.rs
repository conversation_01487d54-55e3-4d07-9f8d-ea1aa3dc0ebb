//! 通用AT命令类型和处理
//! 
//! 这个模块包含了所有调制解调器通用的AT命令类型定义和处理逻辑。

use std::fmt;
use crate::core::errors::{ModemError, ModemResult};
use derive_is_enum_variant::is_enum_variant;

/// AT结果代码，表示命令的完成状态
#[derive(Debug, Clone, PartialEq, Eq, is_enum_variant)]
pub enum AtResultCode {
    /// 命令执行成功，无错误
    Ok,
    /// 连接已建立
    Connect,
    /// 来电
    Ring,
    /// 连接已终止
    NoCarrier,
    /// 通用错误
    Error,
    /// CME错误（通用错误），带有错误代码
    CmeError(u32),
    /// 无拨号音
    NoDialtone,
    /// 接收方忙
    Busy,
    /// 无应答
    NoAnswer,
    /// 命令不支持
    CommandNotSupported,
    /// 参数过多
    TooManyParameters,
}

/// AT命令中使用的各种类型值
#[derive(Debug, <PERSON><PERSON>, PartialEq, Eq, is_enum_variant)]
pub enum AtValue {
    /// 字符串类型值 - 被"引号"包围的文本
    String(String),
    /// 整数
    Integer(u32),
    /// 整数范围
    Range((u32, u32)),
    /// 未类型化的值 - 通常是'裸词'字符串，即不被"引号"包围的字符串
    Unknown(String),
    /// 空值，对应什么都没有
    Empty,
    /// 括号数组
    BracketedArray(Vec<AtValue>),
    /// 非括号数组
    Array(Vec<AtValue>),
}

macro_rules! at_value_impl {
    ($atv:ident, $($var:ident, $refmeth:ident, $mutmeth:ident, $asmeth:ident, $ty:ty),*) => {
        /// 这个impl块提供了从AtValue中提取各种类型的方法
        /// 如果值不是所需类型，返回ModemError::TypeMismatch
        ///
        /// - `as_x` 方法获取self，返回类型或错误
        /// - `get_x` 方法获取&self，返回&引用
        /// - `get_x_mut` 方法获取&mut self，返回&mut引用
        impl $atv {
            $(
                pub fn $refmeth(&self) -> ModemResult<&$ty> {
                    if let $atv::$var(ref i) = *self {
                        Ok(i)
                    }
                    else {
                        Err(ModemError::TypeMismatch)
                    }
                }
                pub fn $mutmeth(&mut self) -> ModemResult<&mut $ty> {
                    if let $atv::$var(ref mut i) = *self {
                        Ok(i)
                    }
                    else {
                        Err(ModemError::TypeMismatch)
                    }
                }
                pub fn $asmeth(self) -> ModemResult<$ty> {
                    if let $atv::$var(i) = self {
                        Ok(i)
                    }
                    else {
                        Err(ModemError::TypeMismatch)
                    }
                }
             )*
        }
    }
}

at_value_impl!(AtValue,
               String, get_string, get_string_mut, as_string, String,
               Integer, get_integer, get_integer_mut, as_integer, u32,
               Range, get_range, get_range_mut, as_range, (u32, u32),
               Unknown, get_unknown, get_unknown_mut, as_unknown, String,
               BracketedArray, get_bracketed_array, get_bracketed_array_mut, as_bracketed_array, Vec<AtValue>,
               Array, get_array, get_array_mut, as_array, Vec<AtValue>);

/// 将AtValue输出，如它在命令行中出现的样子
impl fmt::Display for AtValue {
    fn fmt(&self, f: &mut fmt::Formatter) -> fmt::Result {
        use self::AtValue::*;
        match *self {
            String(ref st) => write!(f, "\"{}\"", st)?,
            Integer(i) => write!(f, "{}", i)?,
            Range((a, b)) => write!(f, "{}-{}", a, b)?,
            Unknown(ref st) => write!(f, "{}", st)?,
            Empty => {},
            BracketedArray(ref val) => {
                write!(f, "(")?;
                for (i, val) in val.iter().enumerate() {
                    let c = if i == 0 { "" } else { "," };
                    write!(f, "{}{}", c, val)?;
                }
                write!(f, ")")?;
            },
            Array(ref val) => {
                for (i, val) in val.iter().enumerate() {
                    let c = if i == 0 { "" } else { "," };
                    write!(f, "{}{}", c, val)?;
                }
            }
        }
        Ok(())
    }
}

/// AT命令响应行之一
#[derive(Debug, Clone, PartialEq, Eq, is_enum_variant)]
pub enum AtResponse {
    /// 作为命令结果发出的信息响应
    InformationResponse {
        param: String,
        response: AtValue
    },
    /// AT结果代码，表示命令完成
    ResultCode(AtResultCode),
    /// 其他未知响应
    Unknown(String)
}

/// 对已发出AT命令的完整响应集
#[derive(Debug, Clone)]
pub struct AtResponsePacket {
    /// 发出的各种AtResponse
    pub responses: Vec<AtResponse>,
    /// 此命令的最终结果代码
    pub status: AtResultCode
}

impl AtResponsePacket {
    /// 提取具有给定resp作为其param的InformationResponse的值（如果存在）
    pub fn extract_named_response_opt(&self, resp: &str) -> ModemResult<Option<&AtValue>> {
        self.assert_ok()?;
        for r in self.responses.iter() {
            if let AtResponse::InformationResponse { ref param, ref response } = *r {
                if resp == param {
                    return Ok(Some(response));
                }
            }
        }
        Ok(None)
    }

    /// 提取具有给定resp作为其param的InformationResponse的值
    pub fn extract_named_response(&self, resp: &str) -> ModemResult<&AtValue> {
        self.extract_named_response_opt(resp)?
            .ok_or(ModemError::ResponseNotFound)
    }

    /// 验证响应是否成功
    pub fn assert_ok(&self) -> ModemResult<()> {
        if self.status == AtResultCode::Ok {
            Ok(())
        } else {
            Err(ModemError::CommandFailed(self.status.clone()))
        }
    }
}

/// AT命令类型
#[derive(Debug, Clone, PartialEq, Eq)]
pub enum AtCommand {
    /// 执行非基本命令或设置参数值
    Equals {
        param: String,
        value: AtValue,
    },
    /// 执行非基本命令
    Execute {
        command: String
    },
    /// 读取参数当前值
    Read {
        param: String
    },
    /// 返回参数的可用值范围
    Test {
        param: String
    },
    /// 执行基本命令
    Basic {
        command: String,
        number: Option<usize>
    },
    /// 发送原始文本
    Text {
        text: String,
        /// 此命令预期的InformationResponse集合
        expected: Vec<String>
    }
}

impl AtCommand {
    /// 返回此命令预期的响应参数列表
    pub fn expected(&self) -> Vec<String> {
        match *self {
            AtCommand::Equals { ref param, .. } => vec![param.clone()],
            AtCommand::Execute { ref command } => vec![command.clone()],
            AtCommand::Read { ref param } => vec![param.clone()],
            AtCommand::Test { ref param } => vec![param.clone()],
            AtCommand::Basic { ref command, .. } => vec![command.clone()],
            AtCommand::Text { ref expected, .. } => expected.clone(),
        }
    }

}

impl fmt::Display for AtCommand {
    fn fmt(&self, f: &mut fmt::Formatter) -> fmt::Result {
        use self::AtCommand::*;
        match *self {
            Equals { ref param, ref value } => write!(f, "AT{}={}", param, value)?,
            Execute { ref command } => write!(f, "AT{}", command)?,
            Read { ref param } => write!(f, "AT{}?", param)?,
            Test { ref param } => write!(f, "AT{}=?", param)?,
            Basic { ref command, ref number } => {
                write!(f, "AT{}", command)?;
                if let Some(n) = *number {
                    write!(f, "{}", n)?;
                }
            },
            Text { ref text, .. } => write!(f, "{}", text)?
        }
        Ok(())
    }
}