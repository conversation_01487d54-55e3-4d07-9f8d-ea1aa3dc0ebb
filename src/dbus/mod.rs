use std::collections::HashMap;
use std::sync::Arc;
use std::time::Duration;
use tokio::sync::Mutex;
use zbus::{interface, connection, Result as ZbusResult};

use crate::core::{ConfigManager, StateManager, ModemManager, RestartType};
use crate::VendorConfig;

/// DBus 接口实现
#[derive(Clone)]
pub struct NetModuleInterface<V: VendorConfig> {
    network_manager: Arc<Mutex<ModemManager<V>>>,
    config_manager: Arc<ConfigManager>,
    state_manager: Arc<StateManager>,
}

impl<V: VendorConfig> NetModuleInterface<V> {
    /// 使用外部的NetworkManager创建接口
    pub fn from_network_manager(
        network_manager: Arc<Mutex<ModemManager<V>>>,
        config_manager: ConfigManager,
        state_manager: StateManager,
    ) -> Self {
        Self {
            network_manager,
            config_manager: Arc::new(config_manager),
            state_manager: Arc::new(state_manager),
        }
    }

    /// 使用共享的NetworkManager创建接口
    pub fn from_shared_network_manager(
        network_manager: Arc<Mutex<ModemManager<V>>>,
        config_manager: ConfigManager,
        state_manager: StateManager,
    ) -> Self {
        Self {
            network_manager,
            config_manager: Arc::new(config_manager),
            state_manager: Arc::new(state_manager),
        }
    }
}

#[interface(name = "mgc.platform.NetModule")]
impl<V: VendorConfig + 'static> NetModuleInterface<V> {
    /// 执行AT指令
    async fn command(&self, cmd: String, timeout: u32) -> String {
        log::info!("Executing AT command: {} with timeout: {}ms", cmd, timeout);

        // 执行 AT 命令
        let manager_guard = self.network_manager.lock().await;
        match manager_guard.execute_at_command(cmd.clone(), Duration::from_millis(timeout as u64)).await {
            Ok(response) => {
                log::debug!("原始AT命令执行成功: {}", response);
                response
            }
            Err(e) => {
                let error_msg = format!("原始AT命令执行失败: {}", e);
                log::error!("{}", error_msg);
                error_msg
            }
        }
    }

    /// 断电重启
    async fn reset(&self) {
        log::info!("Module reset requested");

        let mut manager_guard = self.network_manager.lock().await;
        if let Err(e) = manager_guard.restart(RestartType::Hard).await {
            log::error!("重启失败: {}", e);
        }
    }

    /// 软重启
    async fn factory_reset(&self, code: String) {
        log::info!("Factory reset requested with code: {}", code);

        let mut manager_guard = self.network_manager.lock().await;
        if let Err(e) = manager_guard.restart(RestartType::Soft).await {
            log::error!("软重启失败: {}", e);
        }
    }

    /// 模块状态诊断
    async fn diag(&self) -> String {
        log::info!("Diagnostic information requested");
        let state = self.state_manager.get_state();
        state.generate_diag_info()
    }

    /// 建立连接
    async fn connect(&self) {
        log::info!("Connection requested");
        
        let mut manager_guard = self.network_manager.lock().await;
        if let Err(e) = manager_guard.connect().await {
            log::error!("连接失败: {}", e);
        }
    }

    /// 断开连接
    async fn disconnect(&self) {
        log::info!("Disconnection requested");

        let mut manager_guard = self.network_manager.lock().await;
        // 目前没有专门的断开连接方法，可以通过重启来断开
        if let Err(e) = manager_guard.restart(RestartType::Soft).await {
            log::error!("断开连接失败: {}", e);
        }
    }

    /// 设置电源状态
    async fn set_pwr_state(&self, on: bool) {
        log::info!("Power state change requested: {}", on);
    }

    /// 抓包控制
    async fn capture(&self, params: (bool, u32, u32)) {
        let (enable, file_size_mb, rotation_limit) = params;
        log::info!(
            "Capture control: enable={}, file_size={}MB, rotation_limit={}",
            enable, file_size_mb, rotation_limit
        );
    }

    /// SIM卡状态
    #[zbus(property)]
    async fn sim(&self) -> String {
        let state = self.state_manager.get_state();
        state.sim().to_string()
    }

    /// 卡号
    #[zbus(property)]
    async fn ccid(&self) -> String {
        let state = self.state_manager.get_state();
        state.ccid().to_string()
    }

    /// 运营商
    #[zbus(property)]
    async fn oper(&self) -> String {
        let state = self.state_manager.get_state();
        state.oper().to_string()
    }

    /// 模块型号
    #[zbus(property)]
    async fn model(&self) -> String {
        let state = self.state_manager.get_state();
        state.model().to_string()
    }

    /// 模块版本
    #[zbus(property)]
    async fn revision(&self) -> String {
        let state = self.state_manager.get_state();
        state.revision().to_string()
    }

    /// 国际移动设备识别号
    #[zbus(property)]
    async fn imei(&self) -> String {
        let state = self.state_manager.get_state();
        state.imei().to_string()
    }

    /// 网络制式
    #[zbus(property)]
    async fn mode(&self) -> String {
        let state = self.state_manager.get_state();
        state.mode().to_string()
    }

    /// 信号质量
    #[zbus(property)]
    async fn rsrp(&self) -> i16 {
        let state = self.state_manager.get_state();
        state.rsrp()
    }

    /// 信噪比
    #[zbus(property)]
    async fn sinr(&self) -> i16 {
        let state = self.state_manager.get_state();
        state.sinr()
    }

    /// 经纬度
    #[zbus(property)]
    async fn location(&self) -> Vec<String> {
        let state = self.state_manager.get_state();
        let (lat, lon) = state.location();
        vec![lat.to_string(), lon.to_string()]
    }

    /// 模块连接状态
    #[zbus(property)]
    async fn state(&self) -> String {
        let state = self.state_manager.get_state();
        state.state().to_string()
    }

}

/// Settings 接口实现
#[derive(Clone)]
pub struct NetModuleSettings {
    config_manager: Arc<ConfigManager>,
}

impl NetModuleSettings {
    pub fn new(config_manager: Arc<ConfigManager>) -> Self {
        Self { config_manager }
    }
}

#[interface(name = "mgc.platform.NetModule1.Settings")]
impl NetModuleSettings {
    /// PDP上下文配置
    #[zbus(property)]
    async fn pdp_context(&self) -> HashMap<String, String> {
        let mut map = HashMap::new();
        map.insert("pdp_type".to_string(), "IPV4".to_string());
        map.insert("apn".to_string(), "internet".to_string());
        map
    }

    /// 设置PDP上下文配置
    #[zbus(property)]
    async fn set_pdp_context(&self, value: HashMap<String, String>) {
        log::info!("PDP context update requested: {:?}", value);
    }

    /// 用于通信模组联网状态判断的IP列表
    #[zbus(property)]
    async fn connectivity_test_i_ps(&self) -> Vec<String> {
        vec![
            "*******".to_string(),
            "***************".to_string(),
        ]
    }

    /// 设置用于通信模组联网状态判断的IP列表
    #[zbus(property)]
    async fn set_connectivity_test_i_ps(&self, ips: Vec<String>) {
        log::info!("Connectivity test IPs update requested: {:?}", ips);
    }
}

/// DBus 服务管理器
pub struct DbusService<V: VendorConfig> {
    connection: Option<zbus::Connection>,
    net_module_interface: NetModuleInterface<V>,
    settings_interface: NetModuleSettings,
}

impl<V: VendorConfig> DbusService<V> {
    /// 使用外部的NetworkManager创建DbusService
    pub fn with_shared_network_manager(
        network_manager: Arc<Mutex<ModemManager<V>>>,
        config_manager: ConfigManager,
        state_manager: StateManager,
    ) -> Self {
        let config_manager_arc = Arc::new(config_manager);

        Self {
            connection: None,
            net_module_interface: NetModuleInterface::from_shared_network_manager(
                network_manager,
                (*config_manager_arc).clone(),
                state_manager,
            ),
            settings_interface: NetModuleSettings::new(config_manager_arc),
        }
    }

    /// 启动DBus服务
    pub async fn start(&mut self) -> ZbusResult<()> {
        log::info!("Starting DBus service...");

        // 初始化网络管理器
        let connection = connection::Builder::system()?
            .name("mgc.platform.NetModule1")?
            .serve_at("/mgc/platform/NetModule1", self.net_module_interface.clone())?
            .serve_at("/mgc/platform/NetModule1", self.settings_interface.clone())?
            .build()
            .await?;

        self.connection = Some(connection);
        log::info!("DBus service started successfully");

        Ok(())
    }

    /// 停止DBus服务
    pub async fn stop(&mut self) {
        if let Some(connection) = self.connection.take() {
            drop(connection);
            log::info!("DBus service stopped");
        }
    }
}