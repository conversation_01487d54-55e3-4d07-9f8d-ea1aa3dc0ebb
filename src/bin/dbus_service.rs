use env_logger;
use netmodule::core::{StateManager, ConfigManager};
use netmodule::DbusService;
use tokio::signal;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    /* env_logger::init();

    println!("🚀 网络模块 DBus 服务启动");
    println!("================================\n");

    // 初始化组件
    println!("📡 初始化网络模块组件...");

    let config_manager = ConfigManager::new("config.toml".to_string());
    let state_manager = StateManager::new();

    // 加载配置
    if let Err(e) = config_manager.load() {
        println!("⚠️  配置加载失败，使用默认配置: {}", e);
    }

    println!("✅ 组件初始化成功");

    // 创建并启动 DBus 服务
    let mut dbus_service = DbusService::new(config_manager, state_manager);
    
    match dbus_service.start().await {
        Ok(_) => {
            println!("✅ DBus 服务启动成功");
            println!("📍 DBus 对象路径: /mgc/platform/NetModule1");
            println!("🔌 DBus 接口:");
            println!("   - mgc.platform.NetModule");
            println!("   - mgc.platform.NetModule1.Settings");
        }
        Err(e) => {
            println!("❌ DBus 服务启动失败: {}", e);
            return Err(e.into());
        }
    }

    println!("\n🎯 服务已就绪，等待 DBus 调用...");
    println!("按 Ctrl+C 停止服务");

    // 等待中断信号
    match signal::ctrl_c().await {
        Ok(()) => {
            println!("\n🛑 收到中断信号，正在停止服务...");
        }
        Err(err) => {
            eprintln!("❌ 无法监听中断信号: {}", err);
        }
    }

    // 停止 DBus 服务
    dbus_service.stop().await;
    println!("✅ DBus 服务已停止"); */

    Ok(())
}
