use std::collections::HashMap;
use zbus::{Connection, Result as ZbusResult};

/// DBus 客户端示例，用于测试 NetModule DBus 接口
#[tokio::main]
async fn main() -> ZbusResult<()> {
    println!("🔌 网络模块 DBus 客户端测试");
    println!("================================\n");

    // 连接到系统 DBus
    let connection = Connection::system().await?;
    println!("✅ 已连接到系统 DBus");

    // 创建代理对象
    let proxy = zbus::Proxy::new(
        &connection,
        "mgc.platform.NetModule1",
        "/mgc/platform/NetModule1",
        "mgc.platform.NetModule",
    ).await?;

    let settings_proxy = zbus::Proxy::new(
        &connection,
        "mgc.platform.NetModule1",
        "/mgc/platform/NetModule1",
        "mgc.platform.NetModule1.Settings",
    ).await?;

    println!("✅ DBus 代理对象创建成功\n");

    // 测试基本方法
    println!("🧪 测试基本方法:");
    
    // 测试诊断信息
    println!("📊 获取诊断信息...");
    match proxy.call_method("Diag", &()).await {
        Ok(diag_info) => {
            let diag: String = diag_info.body().deserialize()?;
            println!("   诊断信息: {}", diag);
        }
        Err(e) => println!("   ❌ 获取诊断信息失败: {}", e),
    }

    // 测试 AT 命令
    println!("\n📡 发送 AT 命令...");
    match proxy.call_method("Command", &("ATI", 5000u32)).await {
        Ok(response) => {
            let resp: String = response.body().deserialize()?;
            println!("   AT 命令响应: {}", resp);
        }
        Err(e) => println!("   ❌ AT 命令失败: {}", e),
    }

    // 测试属性读取
    println!("\n📋 测试属性读取:");
    
    let properties = [
        ("Sim", "SIM卡状态"),
        ("Ccid", "卡号"),
        ("Oper", "运营商"),
        ("Model", "模块型号"),
        ("Revision", "模块版本"),
        ("Imei", "IMEI"),
        ("Mode", "网络制式"),
        ("State", "连接状态"),
    ];

    for (prop_name, description) in &properties {
        match proxy.get_property::<String>(prop_name).await {
            Ok(value) => println!("   {}: {}", description, value),
            Err(e) => println!("   ❌ 获取{}失败: {}", description, e),
        }
    }

    // 测试数值属性
    match proxy.get_property::<i16>("Rsrp").await {
        Ok(value) => println!("   信号质量(RSRP): {}", value),
        Err(e) => println!("   ❌ 获取信号质量失败: {}", e),
    }

    match proxy.get_property::<i16>("Sinr").await {
        Ok(value) => println!("   信噪比(SINR): {}", value),
        Err(e) => println!("   ❌ 获取信噪比失败: {}", e),
    }

    // 测试位置信息
    match proxy.get_property::<Vec<String>>("Location").await {
        Ok(location) => {
            if location.len() >= 2 {
                println!("   位置: 纬度={}, 经度={}", location[0], location[1]);
            } else {
                println!("   位置: 数据不完整");
            }
        }
        Err(e) => println!("   ❌ 获取位置信息失败: {}", e),
    }

    // 测试 Settings 接口
    println!("\n⚙️  测试 Settings 接口:");
    
    // 获取 PDP 上下文
    match settings_proxy.get_property::<HashMap<String, String>>("PdpContext").await {
        Ok(pdp_context) => {
            println!("   当前 PDP 上下文:");
            for (key, value) in &pdp_context {
                println!("     {}: {}", key, value);
            }
        }
        Err(e) => println!("   ❌ 获取 PDP 上下文失败: {}", e),
    }

    // 获取连接测试 IP 列表
    match settings_proxy.get_property::<Vec<String>>("ConnectivityTestIPs").await {
        Ok(ips) => {
            println!("   连接测试 IP 列表: {:?}", ips);
        }
        Err(e) => println!("   ❌ 获取连接测试 IP 失败: {}", e),
    }

    // 测试设置 PDP 上下文
    println!("\n🔧 测试设置 PDP 上下文...");
    let mut new_pdp_context = HashMap::new();
    new_pdp_context.insert("pdp_type".to_string(), "IPV4".to_string());
    new_pdp_context.insert("apn".to_string(), "internet".to_string());

    match settings_proxy.set_property("PdpContext", new_pdp_context).await {
        Ok(_) => println!("   ✅ PDP 上下文设置成功"),
        Err(e) => println!("   ❌ PDP 上下文设置失败: {}", e),
    }

    // 测试连接和断开
    println!("\n🔗 测试连接管理:");
    
    println!("   尝试建立连接...");
    match proxy.call_method("Connect", &()).await {
        Ok(_) => println!("   ✅ 连接命令发送成功"),
        Err(e) => println!("   ❌ 连接失败: {}", e),
    }

    // 等待一段时间
    tokio::time::sleep(std::time::Duration::from_secs(2)).await;

    println!("   尝试断开连接...");
    match proxy.call_method("Disconnect", &()).await {
        Ok(_) => println!("   ✅ 断开连接命令发送成功"),
        Err(e) => println!("   ❌ 断开连接失败: {}", e),
    }

    println!("\n✅ DBus 客户端测试完成");

    Ok(())
}
