use netmodule::core::{NetworkManager, ConfigManager, StateManager};
use netmodule::ZTEConfig;

#[tokio::main]
async fn main() {
    env_logger::init();
    
    println!("🧪 测试原始AT命令功能");
    
    // 创建组件
    let config_manager = ConfigManager::new("config.toml".to_string());
    let state_manager = StateManager::new();
    let zte_config = ZTEConfig::default();
    
    // 创建网络管理器（不需要真实的modem）
    let mut network_manager = NetworkManager::new(
        None, // 没有modem，直接使用串口
        config_manager,
        state_manager,
        zte_config,
    );
    
    // 测试原始AT命令
    println!("📡 测试原始AT命令: ATI");
    match network_manager.execute_raw_at_command("ATI".to_string()).await {
        Ok(response) => {
            println!("✅ 原始AT命令成功:");
            println!("响应: {}", response);
        }
        Err(e) => {
            println!("❌ 原始AT命令失败: {}", e);
            println!("这是正常的，因为设备可能不存在");
        }
    }
    
    println!("🎉 测试完成");
}
