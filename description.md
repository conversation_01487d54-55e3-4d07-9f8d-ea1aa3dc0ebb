```markdown
# 4.1.2.1 总体设计描述

通信模组管理负责与板载通信模块交互，执行拨号等AT指令上网，并在检测到模块离网时使用重启模块等手段尝试恢复网络连接，在运行时记录如SIM卡号、信号强度等信息。

## 外部依赖关系
- 使用“网络管理”接口判断网络状态

## 模块分解描述
主要包含4个组件：

1. **进程间通信**  
   - 通过DBus总线接口实现与其他系统模块的交互

2. **核心逻辑**  
   - 负责执行标准的拨号流程、调用外部接口获取网络状态、网络状态异常处理
   - 提供模块诊断信息
   - **功能列表**：
     - 本地配置加载
     - 执行拨号流程
     - 通信模组联网状态判断与维护，状态变化通知
     - 异常记录与诊断
     - 重启间隔控制

3. **AT命令代理**  
   - 队列化管理所有与模块的AT命令交互

4. **logger**  
   - 使用syslog记录程序执行的操作及结果、模块状态、联网状态等信息

---

# 2. 软件接口

## DBus对象路径
`/mgc/platform/NetModule1`

### 接口：`mgc.platform.NetModule`
以DBus形式提供系统内接口和通知机制

#### 方法
| 方法名       | 参数                              | 说明                                                                 |
|--------------|-----------------------------------|----------------------------------------------------------------------|
| Command      | `IN s cmd, IN u timeout, OUT s response` | 执行AT指令                                                            |
| Reset        | `/`                               | 断电重启                                                            |
| FactoryReset | `IN s code`                       | 软重启                                                              |
| Diag         | `OUT s diag_info`                 | 模块状态诊断                                                        |
| Connect      | `/`                               | 建立连接                                                            |
| Disconnect   | `/`                               | 断开连接                                                            |
| SetPwrState  | `IN b on/off`                     | 设置电源状态                                                        |
| Capture      | `IN (buu)`                        | 抓包控制：<br>- b: 开启/关闭<br>- u: 抓包文件大小(MB)<br>- u: 滚动次数限制<br>在min{离网并恢复后的1分钟，离网后的5分钟}时自动停止 |

***

#### 属性
| 属性名     | 访问权限 | 类型    | 说明                   |
|------------|----------|---------|------------------------|
| Sim        | 可读     | s       | SIM卡状态              |
| Ccid       | 可读     | s       | 卡号                   |
| Oper       | 可读     | s       | 运营商                 |
| Model      | 可读     | s       | 模块型号               |
| Revision   | 可读     | s       | 模块版本               |
| Imei       | 可读     | s       | 国际移动设备识别号     |
| Mode       | 可读     | s       | 网络制式               |
| Rsrp       | 可读     | n       | 信号质量               |
| Sinr       | 可读     | n       | 信噪比                 |
| Location   | 可读     | (ss)    | 经纬度                 |
| State      | 可读     | s       | 模块连接状态           |

#### 信号
| 信号名        | 参数 | 说明                             |
|---------------|------|----------------------------------|
| StateChanged  | s    | 连接状态改变（旧状态、新状态和原因） |

---

### 接口：`mgc.platform.NetModule1.Settings`

#### 属性
| 属性名                | 访问权限   | 类型   | 说明                                       |
|-----------------------|------------|--------|--------------------------------------------|
| PdpContext            | 可读可写   | a{sv}  | PDP上下文配置：<br>- `pdp_type`<br>- `apn` |
| ConnectivityTestIPs   | 可读可写   | as     | 用于通信模组联网状态判断的IP列表            |
```