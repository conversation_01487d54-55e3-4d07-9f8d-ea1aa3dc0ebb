[package]
edition = "2021"
name = "netmodule"

version = "0.3.0"

[dependencies]
bytes = "1.5"
derive_is_enum_variant = "0.1"
encoding = "0.2"
env_logger = "0.10"
thiserror = "1.0"
futures = "0.3"
log = "0.4"
nom = "8.0"
num = "0.4"
num-derive = "0.4"
num-traits = "0.2"
rand = "0.8"
serde = { version = "1.0", features = ["derive"] }
toml = "0.8"
tokio = { version = "1.45", features = ["full"] }
tokio-serial = { version = "5.4", features = ["codec"]}
tokio-util = { version = "0.7.15", features = ["codec"] }
async-trait = "0.1.88"
signal-hook = "0.3"
zbus = {version = "5.7", default-features = false, features = ["tokio"]}
zvariant = "5.0"
